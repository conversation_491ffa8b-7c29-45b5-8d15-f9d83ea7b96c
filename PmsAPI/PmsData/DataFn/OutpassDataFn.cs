using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using Microsoft.EntityFrameworkCore;
using PmsCore.PDFGeneration.Interfaces;
using PmsData.Adapters;

namespace PmsData.DataFn
{
    public class OutpassDataFn
    {
        public GlobalDataEntity GlobalData;
        public OutpassDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public PaginatedResult<OutpassMasterVm> GetAllOutpasswithfilters(OutpassFilterVm filters)
        {
            using (var db = new Models.pmsdbContext())
            {
                try
                {
                    // Step 1: Create a more efficient base query
                    var baseQuery = db.OutpassMasters.AsNoTracking();

                    // Step 2: Apply all filters to the base query
                    baseQuery = ApplyFilters(baseQuery, filters, db);

                    // Step 3: Get total count from the filtered query
                    var totalCount = baseQuery.Count();

                    // Step 4: Apply ordering to the base query
                    var orderedQuery = baseQuery.OrderByDescending(x => x.OutpassId);

                    // Step 5: Apply pagination to the ordered query
                    var paginatedQuery = orderedQuery
                        .Skip((filters.PageNumber - 1) * filters.PageSize)
                        .Take(filters.PageSize);

                    // Step 6: Apply joins and projections to the paginated query
                    var pagedResults = ApplyJoinsAndProjections(paginatedQuery, db).ToList();

                    // Step 7: Conditionally load related data if needed
                    if (pagedResults.Any())
                    {
                        LoadRelatedItems(pagedResults, db, filters);
                    }

                    // Step 8: Return paginated result
                    return new PaginatedResult<OutpassMasterVm>
                    {
                        Items = pagedResults,
                        TotalCount = totalCount,
                        PageNumber = filters.PageNumber,
                        PageSize = filters.PageSize
                    };
                }
                catch (Exception ex)
                {
                    // Log the exception
                    Console.WriteLine($"Error in GetAllOutpasswithfilters: {ex.Message}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");

                    return new PaginatedResult<OutpassMasterVm>
                    {
                        Items = new List<OutpassMasterVm>(),
                        TotalCount = 0,
                        PageNumber = filters.PageNumber,
                        PageSize = filters.PageSize
                    };
                }
            }
        }

        // Helper method to apply filters
        private IQueryable<OutpassMaster> ApplyFilters(IQueryable<OutpassMaster> query, OutpassFilterVm filters, pmsdbContext db)
        {
            // Apply OutpassMaster level filters first
            if (filters.OutpassToCustomerId.HasValue && filters.OutpassToCustomerId > 0)
                query = query.Where(x => x.OutpassToCustomerId == filters.OutpassToCustomerId);

            if (!string.IsNullOrEmpty(filters.OutpassNumber))
                query = query.Where(x => x.OutpassNumber.Contains(filters.OutpassNumber));

            if (!string.IsNullOrEmpty(filters.OutpassType))
                query = query.Where(x => x.OutpassType == filters.OutpassType);

            if (!string.IsNullOrEmpty(filters.OutpassTo))
                query = query.Where(x => x.OutpassTo.Contains(filters.OutpassTo));

            if (filters.PurposeId.HasValue && filters.PurposeId > 0)
                query = query.Where(x => x.PurposeId == filters.PurposeId);

            // Date range filter
            if (filters.FromDate.HasValue)
                query = query.Where(x => x.OutpassDate >= filters.FromDate);

            if (filters.ToDate.HasValue)
                query = query.Where(x => x.OutpassDate <= filters.ToDate);

            if (filters.IsOutpassIn.HasValue)
                query = query.Where(x => x.OutpassType == "Returnable" && x.IsOutpassIn == filters.IsOutpassIn);

            // ✅ CRITICAL FIX: Handle ProductId filtering through OutpassItemTable subqueries
            if (filters.ProductId.HasValue && filters.ProductId > 0)
            {
                query = query.Where(outpass =>
                    // Check manual items (OutpassItemTable with StockProductTable -> ProductMaster)
                    db.OutpassItemTables.Any(item =>
                        item.OutpassId == outpass.OutpassId &&
                        item.StockProductId.HasValue &&
                        db.StockProductTables.Any(sp =>
                            sp.StockProductId == item.StockProductId &&
                            sp.ProductId == filters.ProductId
                        )
                    ) ||
                    // Check barcode items (OutpassItemTable with StockLabelTable -> ProductMaster)
                    db.OutpassItemTables.Any(item =>
                        item.OutpassId == outpass.OutpassId &&
                        item.StockLabelId.HasValue &&
                        db.StockLabelTables.Any(sl =>
                            sl.StockLabelId == item.StockLabelId &&
                            sl.ProductId == filters.ProductId
                        )
                    )
                );
            }

            // ✅ ENHANCED: Handle ProductName filtering through OutpassItemTable subquery
            if (!string.IsNullOrEmpty(filters.OutpassProductName))
            {
                query = query.Where(outpass =>
                    db.OutpassItemTables.Any(item =>
                        item.OutpassId == outpass.OutpassId &&
                        item.ProductName.Contains(filters.OutpassProductName)
                    )
                );
            }

            return query;
        }

        // Helper method to apply joins and projections
        private IQueryable<OutpassMasterVm> ApplyJoinsAndProjections(IQueryable<OutpassMaster> query, pmsdbContext db)
        {
            return query
                .GroupJoin(
                    db.UserMasters,
                    a => a.AddedBy,
                    um => um.Email,
                    (a, umGroup) => new { Outpass = a, UserGroup = umGroup })
                .SelectMany(
                    x => x.UserGroup.DefaultIfEmpty(),
                    (x, um) => new { x.Outpass, User = um })
                .GroupJoin(
                    db.TransportCompanyMasters,
                    x => x.Outpass.TransportId,
                    tc => tc.TransportId,
                    (x, tcGroup) => new { x.Outpass, x.User, TransportGroup = tcGroup })
                .SelectMany(
                    x => x.TransportGroup.DefaultIfEmpty(),
                    (x, tc) => new { x.Outpass, x.User, Transport = tc })
                .GroupJoin(
                    db.TransportVehicleTables,
                    x => x.Outpass.VehicleId,
                    vm => vm.VehicleId,
                    (x, vmGroup) => new { x.Outpass, x.User, x.Transport, VehicleGroup = vmGroup })
                .SelectMany(
                    x => x.VehicleGroup.DefaultIfEmpty(),
                    (x, vm) => new { x.Outpass, x.User, x.Transport, Vehicle = vm })
                .Select(x => new OutpassMasterVm
                {
                    OutpassId = x.Outpass.OutpassId,
                    OutpassTo = x.Outpass.OutpassTo,
                    OutpassDate = x.Outpass.OutpassDate,
                    OutpassNumber = x.Outpass.OutpassNumber,
                    OutpassType = x.Outpass.OutpassType,
                    Purpose = x.Outpass.Purpose,
                    PurposeId = x.Outpass.PurposeId,
                    Remark = x.Outpass.Remark,
                    IsOutpassIn = x.Outpass.IsOutpassIn,
                    AddedBy = x.Outpass.AddedBy,
                    AddedDate = x.Outpass.AddedDate,
                    OutpassToCustomerId = x.Outpass.OutpassToCustomerId,
                    ExpectedReturnDate = x.Outpass.ExpectedReturnDate,
                    Status = x.Outpass.Status,
                    TransportId = x.Outpass.TransportId,
                    TransportName = x.Transport != null ? x.Transport.TransportCompanyName : null,
                    VehicleId = x.Outpass.VehicleId,
                    VehicleNumber = x.Vehicle != null ? x.Vehicle.VehicleNumber : null,
                    IsGateIn = x.Outpass.IsGateIn,
                    CreateMode = x.Outpass.CreateMode,
                    OutpassItems = new List<OutpassItemTableVm>()
                });
        }

        // Helper method to load related items
        private void LoadRelatedItems(List<OutpassMasterVm> pagedResults, pmsdbContext db, OutpassFilterVm filters)
        {
            var outpassIds = pagedResults.Select(x => x.OutpassId).ToList();

            // Create a base query for items
            var itemsBaseQuery = db.OutpassItemTables
                .AsNoTracking()
                .Where(op => outpassIds.Contains(op.OutpassId.Value));

            // ✅ REMOVED: Product filtering is now handled at the OutPass master level
            // This ensures we only load items for OutPasses that already match the product criteria

            // Apply joins and projections
            var outpassItems = itemsBaseQuery
                .GroupJoin(
                    db.RackMasters,
                    op => op.RackId,
                    r => r.RackId,
                    (op, rGroup) => new { OutpassItem = op, RackGroup = rGroup })
                .SelectMany(
                    x => x.RackGroup.DefaultIfEmpty(),
                    (x, r) => new { x.OutpassItem, Rack = r })
                .GroupJoin(
                    db.StoreMasters,
                    x => x.Rack.StoreId,
                    s => s.StoreId,
                    (x, sGroup) => new { x.OutpassItem, x.Rack, StoreGroup = sGroup })
                .SelectMany(
                    x => x.StoreGroup.DefaultIfEmpty(),
                    (x, s) => new { x.OutpassItem, x.Rack, Store = s })
                .GroupJoin(
                    db.RackMasters,
                    x => x.OutpassItem.ReturnedRackId,
                    rr => rr.RackId,
                    (x, rrGroup) => new { x.OutpassItem, x.Rack, x.Store, ReturnedRackGroup = rrGroup })
                .SelectMany(
                    x => x.ReturnedRackGroup.DefaultIfEmpty(),
                    (x, rr) => new { x.OutpassItem, x.Rack, x.Store, ReturnedRack = rr })
                .GroupJoin(
                    db.StoreMasters,
                    x => x.ReturnedRack.StoreId,
                    rs => rs.StoreId,
                    (x, rsGroup) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, ReturnedStoreGroup = rsGroup })
                .SelectMany(
                    x => x.ReturnedStoreGroup.DefaultIfEmpty(),
                    (x, rs) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, ReturnedStore = rs })
                .GroupJoin(
                    db.UserMasters,
                    x => x.OutpassItem.ReturnCompletedBy,
                    um => um.Email,
                    (x, umGroup) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, UserGroup = umGroup })
                .SelectMany(
                    x => x.UserGroup.DefaultIfEmpty(),
                    (x, um) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, User = um })
                // Add join with StockProducts table
                .GroupJoin(
                    db.StockProductTables,
                    x => x.OutpassItem.StockProductId,
                    sp => sp.StockProductId,
                    (x, spGroup) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, x.User, StockProductGroup = spGroup })
                .SelectMany(
                    x => x.StockProductGroup.DefaultIfEmpty(),
                    (x, sp) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, x.User, StockProduct = sp })
                // Add join with StockMasters table
                .GroupJoin(
                    db.StockMasters,
                    x => x.StockProduct.StockId,
                    sm => sm.StockId,
                    (x, smGroup) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, x.User, x.StockProduct, StockMasterGroup = smGroup })
                .SelectMany(
                    x => x.StockMasterGroup.DefaultIfEmpty(),
                    (x, sm) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, x.User, x.StockProduct, StockMaster = sm })
                .Select(x => new OutpassItemTableVm
                {
                    OutpassItemId = x.OutpassItem.OutpassItemId,
                    OutpassId = x.OutpassItem.OutpassId,
                    ProductName = x.OutpassItem.ProductName,
                    StockProductId = x.OutpassItem.StockProductId,
                    RackId = x.OutpassItem.RackId,
                    Quantity = x.OutpassItem.Quantity,
                    Amount = x.OutpassItem.Amount,
                    Unit = x.OutpassItem.Unit,
                    RackName = x.Rack.RackName,
                    StoreName = x.Store.StoreName,
                    BatchNo = x.StockMaster.Batch,
                    // OutpassType = pagedResults.FirstOrDefault(o => o.OutpassId == x.OutpassItem.OutpassId).OutpassType,
                    ReturnCompletedBy = x.User != null ? new UserMasterVm { Name = x.User.Name } : null,
                    ReturnCompletedDate = x.OutpassItem.ReturnCompletedDate,
                    ReturnedQuantity = x.OutpassItem.ReturnedQuantity,
                    ReturnedRackId = x.OutpassItem.ReturnedRackId,
                    ReturnedRackName = x.ReturnedRack.RackName,
                    ReturnedStoreName = x.ReturnedStore.StoreName,
                })
                .ToList();

            // Group items by OutpassId and assign to respective Outpass objects
            var itemsByOutpassId = outpassItems.GroupBy(item => item.OutpassId);
            foreach (var group in itemsByOutpassId)
            {
                var outpass = pagedResults.FirstOrDefault(o => o.OutpassId == group.Key);
                if (outpass != null)
                {
                    outpass.OutpassItems = group.ToList();
                }
            }
        }
        public OutpassMasterVm GetOutpassById(long OutpassId)
        {
            OutpassMasterVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.OutpassMasters
                       join um in db.UserMasters on a.AddedBy equals um.Email
                       where a.OutpassId == OutpassId
                       select new OutpassMasterVm
                       {
                           OutpassId = a.OutpassId,
                           OutpassTo = a.OutpassTo,
                           OutpassDate = a.OutpassDate,
                           OutpassNumber = a.OutpassNumber,
                           OutpassType = a.OutpassType,
                           Purpose = a.Purpose,
                           PurposeId = a.PurposeId,
                           Remark = a.Remark,
                           IsOutpassIn = a.IsOutpassIn,
                           AddedBy = um.Name,
                           AddedDate = a.AddedDate,
                           ExpectedReturnDate = a.ExpectedReturnDate,
                           OutpassToCustomerId = a.OutpassToCustomerId,
                           Status = a.Status,
                           TransportId = a.TransportId,
                           VehicleId = a.VehicleId,
                           IsGateIn = a.IsGateIn,
                           CreateMode = a.CreateMode ?? "Manual", // ✅ BACKWARD COMPATIBILITY: Default to Manual for existing OutPasses
                           ApprovedByName = (from osh in db.OutpassStatusHistories
                                             join um in db.UserMasters on osh.AddedBy equals um.Email
                                             where osh.OutpassId == a.OutpassId && osh.Status == "Approved"
                                             select um.Name).FirstOrDefault(),
                           // ✅ CRITICAL FIX: Get StockProductIds that have barcode movement history first
                           OutpassItems = (from op in db.OutpassItemTables
                                           join sp in db.StockProductTables on op.StockProductId equals sp.StockProductId into spd
                                           from sp in spd.DefaultIfEmpty()
                                           join sm in db.StockMasters on sp.StockId equals sm.StockId into smd
                                           from sm in smd.DefaultIfEmpty()
                                           join p in db.ProductMasters on sp.ProductId equals p.ProductId into pd
                                           from p in pd.DefaultIfEmpty()
                                           join r in db.RackMasters on op.RackId equals r.RackId into rc
                                           from r in rc.DefaultIfEmpty()
                                           join s in db.StoreMasters on r.StoreId equals s.StoreId into sc
                                           from s in sc.DefaultIfEmpty()
                                           join rr in db.RackMasters on op.ReturnedRackId equals rr.RackId into rrc
                                           from rr in rrc.DefaultIfEmpty()
                                           join rs in db.StoreMasters on rr.StoreId equals rs.StoreId into rsc
                                           from rs in rsc.DefaultIfEmpty()
                                           join um in db.UserMasters on op.ReturnCompletedBy equals um.Email into umd
                                           from um in umd.DefaultIfEmpty()
                                           where op.OutpassId == a.OutpassId
                                           // ✅ OPTIMIZED: Clean separation using StockLabelId
                                           // Manual items have NULL StockLabelId, Barcode items have valid StockLabelId
                                           && op.StockLabelId == null
                                           select new OutpassItemTableVm
                                           {
                                               OutpassItemId = op.OutpassItemId,
                                               OutpassId = op.OutpassId,
                                               ProductId = p.ProductId,
                                               ProductName = op.ProductName,
                                               StockProductId = op.StockProductId,
                                               RackId = op.RackId,
                                               Quantity = op.Quantity,
                                               Amount = op.Amount,
                                               Unit = op.Unit,
                                               RackName = r.RackName,
                                               StoreName = s.StoreName,
                                               BatchNo = sm.Batch,
                                               OutpassType = a.OutpassType,
                                               ReturnCompletedBy = new UserMasterVm
                                               {
                                                   Name = um.Name
                                               },
                                               ReturnCompletedDate = op.ReturnCompletedDate,
                                               ReturnedQuantity = op.ReturnedQuantity,
                                               ReturnedRackId = op.ReturnedRackId,
                                               ReturnedRackName = rr.RackName,
                                               ReturnedStoreName = rs.StoreName
                                           }).OrderBy(x => x.OutpassItemId).ToList(),
                       }).FirstOrDefault();

                // ✅ FIX: Populate BarcodeItems collection for UI display with correct amount calculation
                if (res != null)
                {
                    res.BarcodeDetails = (from sl in db.StockLabelTables
                                          join sp in db.StockProductTables on sl.StockProductId equals sp.StockProductId
                                          join sm in db.StockMasters on sp.StockId equals sm.StockId
                                          join store in db.StoreMasters on sl.CurrentStoreId equals store.StoreId into storeGroup
                                          from st in storeGroup.DefaultIfEmpty()
                                          join rack in db.RackMasters on sl.CurrentRackId equals rack.RackId into rackGroup
                                          from rk in rackGroup.DefaultIfEmpty()
                                          where db.OutpassItemTables.Any(oi =>
                                              oi.StockLabelId == sl.StockLabelId &&
                                              oi.OutpassId == OutpassId)
                                          select new StockLabelTableVm
                                          {
                                              StockLabelId = sl.StockLabelId,
                                              SerialNo = sl.SerialNo,
                                              ShortCode = sl.ShortCode,
                                              LabelStatus = sl.LabelStatus,
                                              StockProductId = sl.StockProductId,
                                              ProductId = sl.ProductId,
                                              Quantity = sl.Quantity,
                                              PackagingUnit = sl.PackagingUnit,
                                              StoreName = st.StoreName ?? "",
                                              RackName = rk.RackName ?? "",
                                              CurrentStoreId = sl.CurrentStoreId ?? 0,
                                              CurrentRackId = sl.CurrentRackId ?? 0,
                                              IsActive = sl.IsActive ?? true,
                                              StockId = sl.StockId,
                                              OriginalLabelId = sl.OriginalLabelId,
                                              MfgDate = sl.MfgDate,
                                              ExpiryDate = sl.ExpiryDate,
                                              AddedBy = sl.AddedBy,
                                              AddedDate = sl.AddedDate,
                                              UpdatedBy = sl.UpdatedBy,
                                              UpdatedDate = sl.UpdatedDate,
                                              NumberOfLabels = 1,
                                              AllocationId = sl.AllocationId,
                                              InspectionStatus = sl.InspectionStatus
                                          }).OrderBy(x => x.StockLabelId).ToList();
                    // Initialize BarcodeItems as empty list first
                    res.BarcodeItems = new List<OutpassBarcodeItemVm>();

                    if (res.BarcodeDetails != null && res.BarcodeDetails.Count > 0)
                    {
                        try
                        {
                            res.BarcodeItems = (from bd in res.BarcodeDetails
                                                join sp in db.StockProductTables on bd.StockProductId equals sp.StockProductId into spGroup
                                                from sp in spGroup.DefaultIfEmpty()
                                                join sm in db.StockMasters on sp.StockId equals sm.StockId into smGroup
                                                from sm in smGroup.DefaultIfEmpty()
                                                join pm in db.ProductMasters on bd.ProductId equals pm.ProductId into pmGroup
                                                from pm in pmGroup.DefaultIfEmpty()
                                                select new OutpassBarcodeItemVm
                                                {
                                                    StockLabelId = bd.StockLabelId,
                                                    SerialNo = bd.SerialNo ?? "",
                                                    ShortCode = bd.ShortCode ?? "",
                                                    ProductName = pm?.ProductName ?? "",
                                                    ProductId = bd.ProductId,
                                                    StockProductId = bd.StockProductId,
                                                    Quantity = bd.Quantity ?? 1,
                                                    PackagingUnit = bd.PackagingUnit ?? "",
                                                    Unit = sp?.Unit ?? "",
                                                    BatchNo = sm?.Batch ?? "",
                                                    CurrentStoreId = bd.CurrentStoreId,
                                                    CurrentRackId = bd.CurrentRackId,
                                                    StoreName = bd.StoreName ?? "",
                                                    RackName = bd.RackName ?? "",
                                                    PricePerUnit = sp?.PricePerUnit ?? 0
                                                }).OrderBy(x => x.StockLabelId).ToList();

                            // ✅ CRITICAL FIX: Separate processing for barcode items - NO cross-contamination with OutpassItemTables
                            // ✅ ROBUST FIX: Get return data directly from OutpassItemTable using StockLabelId relationship
                            res.BarcodeItems.ForEach(item =>
                            {
                                // Direct query using StockLabelId - no more fragile regex parsing!
                                var outpassItem = (from op in db.OutpassItemTables
                                                   join a in db.RackMasters on op.ReturnedRackId equals a.RackId into am
                                                   from a in am.DefaultIfEmpty()
                                                   join s in db.StoreMasters on a.StoreId equals s.StoreId into sm
                                                   from s in sm.DefaultIfEmpty()
                                                   join um in db.UserMasters on op.ReturnCompletedBy equals um.Email into umd
                                                   from um in umd.DefaultIfEmpty()
                                                   where op.StockLabelId == item.StockLabelId && op.OutpassId == OutpassId
                                                   select new
                                                   {
                                                       op.Amount,
                                                       op.OutpassItemId,
                                                       op.ReturnedQuantity,
                                                       op.ReasonForLessQuantity,
                                                       s.StoreName,
                                                       a.RackName,
                                                       um.Name,
                                                       op.ReturnCompletedDate
                                                   }).FirstOrDefault();

                                if (outpassItem != null)
                                {
                                    item.Amount = outpassItem.Amount ?? 0;
                                    item.OutpassItemId = outpassItem.OutpassItemId;
                                    item.ReturnedQuantity = outpassItem.ReturnedQuantity ?? 0;
                                    item.ReasonForLessQuantity = outpassItem.ReasonForLessQuantity ?? "";
                                    item.ReturnedStoreName = outpassItem.StoreName ?? "";
                                    item.ReturnedRackName = outpassItem.RackName ?? "";
                                    item.ReturnCompletedBy = outpassItem.Name ?? null;
                                    item.ReturnCompletedDate = outpassItem.ReturnCompletedDate;
                                }
                            });
                        }
                        catch (Exception)
                        {
                            // Log the exception but don't fail the entire request
                            // Initialize empty list if transformation fails
                            res.BarcodeItems = new List<OutpassBarcodeItemVm>();
                        }
                    }
                }

                // ✅ CRITICAL FIX: Ensure data consistency based on CreateMode
                if (res != null)
                {
                    ValidateAndCleanOutpassData(res);
                }
            }
            return res;
        }

        /// <summary>
        /// ✅ OPTIMIZED: Validates OutPass data consistency without aggressive cleaning
        /// Preserves data needed for StockLabelId-based queries while ensuring logical consistency
        /// </summary>
        private void ValidateAndCleanOutpassData(OutpassMasterVm outpass)
        {
            if (outpass == null) return;

            switch (outpass.CreateMode)
            {
                case "Manual":
                    // Manual mode: Warn if barcode items exist but don't remove them (for debugging)
                    if (outpass.BarcodeItems?.Any() == true)
                    {
                        // Log warning but preserve data for investigation
                        System.Diagnostics.Debug.WriteLine($"WARNING: Manual mode OutPass {outpass.OutpassId} has {outpass.BarcodeItems.Count} barcode items");
                    }
                    // Only clear BarcodeDetails (UI-specific data)
                    outpass.BarcodeDetails = new List<StockLabelTableVm>();
                    break;

                case "Barcode":
                    // Barcode mode: Warn if manual items exist but preserve for StockLabelId queries
                    if (outpass.OutpassItems?.Any() == true)
                    {
                        // Check if these are legitimate OutpassItemTable records for barcode items
                        var barcodeStockLabelIds = outpass.BarcodeItems?.Select(bi => bi.StockLabelId).ToList() ?? new List<long>();
                        var manualItemsWithStockLabels = outpass.OutpassItems.Where(oi =>
                            barcodeStockLabelIds.Any(id => id > 0)).ToList();

                        if (manualItemsWithStockLabels.Any())
                        {
                            // These are legitimate OutpassItemTable records for barcode items - keep them
                            System.Diagnostics.Debug.WriteLine($"INFO: Barcode mode OutPass {outpass.OutpassId} has {manualItemsWithStockLabels.Count} OutpassItemTable records for barcode items");
                        }
                        else
                        {
                            // These might be erroneous manual items
                            System.Diagnostics.Debug.WriteLine($"WARNING: Barcode mode OutPass {outpass.OutpassId} has {outpass.OutpassItems.Count} manual items");
                        }
                    }
                    break;

                case "Mixed":
                    // Mixed mode: Validate that both types exist
                    var hasManualItems = outpass.OutpassItems?.Any() == true;
                    var hasBarcodeItems = outpass.BarcodeItems?.Any() == true;

                    if (!hasManualItems && !hasBarcodeItems)
                    {
                        System.Diagnostics.Debug.WriteLine($"WARNING: Mixed mode OutPass {outpass.OutpassId} has no items");
                    }
                    break;

                default:
                    // Default to Manual mode behavior for backward compatibility
                    outpass.BarcodeDetails = new List<StockLabelTableVm>();
                    break;
            }
        }
        public ApiFunctionResponseVm AddOutpasss(OutpassMasterVm Outpass)
        {
            // Route to appropriate method based on create mode
            if (!string.IsNullOrEmpty(Outpass.CreateMode))
            {
                if (Outpass.CreateMode == "Barcode")
                {
                    return AddBarcodeOutpass(Outpass);
                }
                else if (Outpass.CreateMode == "Mixed")
                {
                    return AddMixedModeOutpass(Outpass);
                }
            }

            // Default manual mode
            return AddManualOutpass(Outpass);
        }

        private ApiFunctionResponseVm AddManualOutpass(OutpassMasterVm Outpass)
        {
            using (var db = new Models.pmsdbContext())
            {
                OutpassMaster mm = new OutpassMaster
                {
                    OutpassTo = Outpass.OutpassTo,
                    OutpassDate = Outpass.OutpassDate,
                    OutpassType = Outpass.OutpassType,
                    Purpose = Outpass.Purpose,
                    PurposeId = Outpass.PurposeId,
                    Remark = Outpass.Remark,
                    IsOutpassIn = false,
                    AddedBy = Outpass.AddedBy,
                    AddedDate = DateTime.Now,
                    OutpassToCustomerId = Outpass.OutpassToCustomerId,
                    ExpectedReturnDate = Outpass.ExpectedReturnDate,
                    Status = OutpassStatus.ApprovalPending,
                    TransportId = Outpass.TransportId,
                    VehicleId = Outpass.VehicleId,
                    IsGateIn = Outpass.VehicleId > 0,
                    CreateMode = "Manual"
                };
                db.OutpassMasters.Add(mm);
                db.SaveChanges();
                mm.OutpassNumber = "Z-DC/" + mm.OutpassId;
                db.SaveChanges();

                if (Outpass.VehicleId > 0)
                {
                    GateInTable sm = db.GateInTables.FirstOrDefault(x => x.VehicleId == Outpass.VehicleId && x.GateIn == true && x.GateOut != true);
                    if (sm == null)
                    {
                        sm = new GateInTable();
                        sm.VehicleId = Outpass.VehicleId.Value;
                        sm.GateInDate = System.DateTime.Now;
                        sm.GateInPerson = "System";
                        sm.GateInPersonContact = "System";
                        sm.GateIn = true;
                        sm.AddedBy = GlobalData.loggedInUser;
                        sm.AddedDate = System.DateTime.Now;
                        sm.Type = "Outpass Dispatch";
                        db.GateInTables.Add(sm);
                        db.SaveChanges();
                    }
                    GateInInvoiceMappingTable gi = new()
                    {
                        OutpassId = mm.OutpassId,
                        GateInId = sm.GateInId
                    };
                    db.GateInInvoiceMappingTables.Add(gi);
                    db.SaveChanges();
                }

                db.OutpassStatusHistories.Add(
                    new OutpassStatusHistory
                    {
                        OutpassId = mm.OutpassId,
                        Status = OutpassStatus.ApprovalPending,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now,
                        Remark = Outpass.Remark
                    }
                );

                foreach (var op in Outpass.OutpassItems)
                {
                    OutpassItemTable spt = new OutpassItemTable
                    {
                        OutpassItemId = op.OutpassItemId,
                        OutpassId = mm.OutpassId,
                        ProductName = op.ProductName,
                        StockProductId = op.StockProductId,
                        RackId = op.RackId,
                        Quantity = op.Quantity,
                        Amount = op.Amount,
                        Unit = op.Unit
                    };
                    db.OutpassItemTables.Add(spt);
                    // if (op.StockProductId > 0)
                    // {
                    //     var removestock = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == op.StockProductId && x.RackId == op.RackId);
                    //     removestock.Quantity = removestock.Quantity - op.Quantity.Value;
                    //     if (removestock.Quantity == 0)
                    //     {
                    //         db.StockProductAllocationTables.Remove(removestock);
                    //     }
                    // }
                }
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm ModifyOutpass(OutpassMasterVm Outpass)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var existing = db.OutpassMasters.FirstOrDefault(x => x.OutpassId == Outpass.OutpassId);
                var PreviousExpectedReturnDate = existing.ExpectedReturnDate;
                if (existing != null && Outpass.VehicleId > 0)
                {
                    existing.VehicleId = Outpass.VehicleId;
                    existing.TransportId = Outpass.TransportId;
                    existing.IsGateIn = true;
                    db.SaveChanges();
                }
                else if (existing != null && Outpass.ExpectedReturnDate != null)
                {
                    existing.ExpectedReturnDate = Outpass.ExpectedReturnDate;
                    db.SaveChanges();

                    db.OutpassStatusHistories.Add(
                        new OutpassStatusHistory
                        {
                            OutpassId = Outpass.OutpassId,
                            Status = OutpassStatus.ReturnExtended,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now,
                            Remark = "Return Extended from " + TimeZoneHelper.ConvertToTimeZone(PreviousExpectedReturnDate.Value, TimeZoneId.IndiaStandardTime) + " To " + TimeZoneHelper.ConvertToTimeZone(Outpass.ExpectedReturnDate.Value, TimeZoneId.IndiaStandardTime) + ". Due to: " + Outpass.Remark,
                        });
                }

                if (Outpass.VehicleId > 0)
                {
                    GateInTable sm = db.GateInTables.FirstOrDefault(x => x.VehicleId == Outpass.VehicleId && x.GateIn == true && x.GateOut != true);
                    if (sm == null)
                    {
                        sm = new GateInTable();
                        sm.VehicleId = Outpass.VehicleId.Value;
                        sm.GateInDate = System.DateTime.Now;
                        sm.GateInPerson = "System";
                        sm.GateInPersonContact = "System";
                        sm.GateIn = true;
                        sm.GateOut = false;
                        sm.AddedBy = GlobalData.loggedInUser;
                        sm.AddedDate = System.DateTime.Now;
                        sm.Type = "Outpass Dispatch";
                        db.GateInTables.Add(sm);
                        db.SaveChanges();
                    }
                    GateInInvoiceMappingTable gi = new()
                    {
                        OutpassId = Outpass.OutpassId,
                        GateInId = sm.GateInId
                    };
                    db.GateInInvoiceMappingTables.Add(gi);
                    var vehicleInfo = (from tc in db.TransportCompanyMasters
                                       join vm in db.TransportVehicleTables on tc.TransportId equals vm.TransportId
                                       where vm.VehicleId == Outpass.VehicleId
                                       select new
                                       {
                                           tc.TransportCompanyName,
                                           vm.VehicleNumber,
                                           vm.VehicleType
                                       }).FirstOrDefault();
                    if (vehicleInfo != null)
                    {
                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = Outpass.OutpassId,
                                Status = OutpassStatus.VehicleAssigned,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = "Vehicle Type: " + vehicleInfo.VehicleType + " with Registration No.: " + vehicleInfo.VehicleNumber + " from " + vehicleInfo.TransportCompanyName + " Assigned."
                            });
                    }
                    else
                    {
                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = Outpass.OutpassId,
                                Status = OutpassStatus.VehicleAssigned,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = "Vehicle assigned"
                            });
                    }
                }
                db.SaveChanges();
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Outpass Updated Successfully.");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
            }

        }

        public ApiFunctionResponseVm InOutpass(OutpassMasterVm Outpass)
        {
            using var db = new pmsdbContext();
            var transactionDate = DateTime.Now;
            using var transaction = db.Database.BeginTransaction();
            try
            {
                // 1. COMPREHENSIVE VALIDATION LOGIC
                var validationErrors = new List<string>();

                // ✅ CRITICAL FIX: Validate data consistency to prevent cross-contamination
                var outpassMaster = db.OutpassMasters.FirstOrDefault(x => x.OutpassId == Outpass.OutpassId);
                if (outpassMaster != null)
                {
                    // Validate based on CreateMode
                    if (outpassMaster.CreateMode == "Barcode")
                    {
                        if (Outpass.OutpassItems != null && Outpass.OutpassItems.Any())
                        {
                            validationErrors.Add("Barcode mode OutPass should not contain manual items. Please check frontend data.");
                        }
                        if (Outpass.BarcodeItems == null || !Outpass.BarcodeItems.Any())
                        {
                            validationErrors.Add("Barcode mode OutPass must contain barcode items.");
                        }
                    }
                    else if (outpassMaster.CreateMode == "Manual")
                    {
                        if (Outpass.BarcodeItems != null && Outpass.BarcodeItems.Any())
                        {
                            validationErrors.Add("Manual mode OutPass should not contain barcode items. Please check frontend data.");
                        }
                        if (Outpass.OutpassItems == null || !Outpass.OutpassItems.Any())
                        {
                            validationErrors.Add("Manual mode OutPass must contain manual items.");
                        }
                    }
                    // Mixed mode can have both, so no additional validation needed
                }

                // Validate manual items
                if (Outpass.OutpassItems != null && Outpass.OutpassItems.Any())
                {
                    foreach (var item in Outpass.OutpassItems)
                    {
                        // Validate returned quantity
                        if (item.ReturnedQuantity > item.Quantity)
                        {
                            validationErrors.Add($"Product '{item.ProductName}' (ID: {item.OutpassItemId}): Returned quantity ({item.ReturnedQuantity}) cannot exceed original quantity ({item.Quantity})");
                        }

                        // Validate reason for less quantity
                        if (item.ReturnedQuantity < item.Quantity && string.IsNullOrWhiteSpace(item.ReasonForLessQuantity))
                        {
                            validationErrors.Add($"Product '{item.ProductName}' (ID: {item.OutpassItemId}): Reason is required when returning less quantity");
                        }
                    }
                }

                // Validate barcode items
                if (Outpass.BarcodeItems != null && Outpass.BarcodeItems.Any())
                {
                    foreach (var barcodeItem in Outpass.BarcodeItems)
                    {
                        // Validate returned quantity
                        if (barcodeItem.ReturnedQuantity > barcodeItem.Quantity)
                        {
                            validationErrors.Add($"Barcode Product '{barcodeItem.ProductName}' (Serial: {barcodeItem.SerialNo}): Returned quantity ({barcodeItem.ReturnedQuantity}) cannot exceed original quantity ({barcodeItem.Quantity})");
                        }

                        // Validate reason for less quantity
                        if (barcodeItem.ReturnedQuantity < barcodeItem.Quantity && string.IsNullOrWhiteSpace(barcodeItem.ReasonForLessQuantity))
                        {
                            validationErrors.Add($"Barcode Product '{barcodeItem.ProductName}' (Serial: {barcodeItem.SerialNo}): Reason is required when returning less quantity");
                        }
                    }
                }

                // Return validation errors before processing any consumption logic
                if (validationErrors.Any())
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, string.Join("; ", validationErrors));
                }

                // Initialize StockDataFn for proper label lifecycle management
                var stockDataFn = new StockDataFn(GlobalData);

                // 2. UPDATE OUTPASS STATUS (Existing Logic)
                OutpassMaster mm = db.OutpassMasters.FirstOrDefault(x => x.OutpassId == Outpass.OutpassId);
                if (mm == null)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "OutPass not found");
                }

                mm.IsOutpassIn = true;
                mm.Status = OutpassStatus.ReturnCompleted;
                db.SaveChanges();

                // 3. PROCESS MANUAL ITEMS - Enhanced with Consumption Tracking
                if (Outpass.OutpassItems != null && Outpass.OutpassItems.Any())
                {
                    foreach (var op in Outpass.OutpassItems)
                    {
                        // Update OutpassItemTable with returned quantities (Existing Logic)
                        OutpassItemTable spt = db.OutpassItemTables.FirstOrDefault(x => x.OutpassItemId == op.OutpassItemId);
                        if (spt != null)
                        {
                            // ✅ ENHANCED LOGGING: Debug rack assignment for manual items
                            Console.WriteLine($"Updating Manual OutpassItem {op.OutpassItemId}: " +
                                            $"ReturnedRackId {op.ReturnedRackId} (was {spt.ReturnedRackId})");

                            spt.ReturnedQuantity = op.ReturnedQuantity;
                            spt.ReturnCompletedBy = GlobalData.loggedInUser;
                            spt.ReturnCompletedDate = transactionDate;
                            spt.ReturnedRackId = op.ReturnedRackId;

                            Console.WriteLine($"Updated Manual OutpassItem: ReturnedRackId now {spt.ReturnedRackId}");
                        }
                        else
                        {
                            Console.WriteLine($"WARNING: No OutpassItemTable found for OutpassItemId {op.OutpassItemId}");
                        }

                        // NEW: CONSUMPTION TRACKING IMPLEMENTATION
                        if (op.ReturnedQuantity < op.Quantity)
                        {
                            var consumedQuantity = op.Quantity - op.ReturnedQuantity;
                            //Purpose column support 200 characters max
                            var purpose = $"Returned less qty Via Out Pass No.: {mm.OutpassNumber} with Reason: {op.ReasonForLessQuantity}";

                            var stockProduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == op.StockProductId);

                            var consumeRecord = new ConsumeStockProductMaster
                            {
                                ProductId = op.ProductId ?? 0,
                                StockProductId = op.StockProductId,
                                StockId = stockProduct?.StockId,
                                Quantity = consumedQuantity.Value,
                                Purpose = purpose,
                                ConsumedDate = transactionDate,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = transactionDate,
                                RackId = op.RackId,
                                StoreId = GetStoreIdFromRackId(db, op.RackId),
                                Unit = op.Unit,
                                IsDamaged = false
                            };

                            db.ConsumeStockProductMasters.Add(consumeRecord);
                        }

                        // Existing stock allocation logic
                        if (op.StockProductId > 0)
                        {
                            var stockrec = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == op.StockProductId && x.RackId == op.ReturnedRackId && x.InspectionType == PmsCommon.PMSStatus.Accepted);
                            if (stockrec == null)
                            {
                                var spa = new StockProductAllocationTable()
                                {
                                    StockProductId = op.StockProductId.Value,
                                    Quantity = op.ReturnedQuantity.Value,
                                    InspectionType = PmsCommon.PMSStatus.Accepted,
                                    RackId = op.ReturnedRackId.Value
                                };
                                db.StockProductAllocationTables.Add(spa);
                            }
                            else
                            {
                                stockrec.Quantity = stockrec.Quantity + op.ReturnedQuantity.Value;
                            }

                            // NEW: Update master quantity
                            var stockProduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == op.StockProductId);
                            if (stockProduct != null)
                            {
                                stockProduct.Quantity = stockProduct.Quantity + op.ReturnedQuantity.Value;
                            }
                            db.SaveChanges();
                        }
                    }
                }

                // 4. PROCESS BARCODE ITEMS - Update OutpassItemTable
                if (Outpass.BarcodeItems != null && Outpass.BarcodeItems.Any())
                {
                    foreach (var barcodeItem in Outpass.BarcodeItems)
                    {
                        // ✅ ROBUST FIX: Update OutpassItemTable directly with return data
                        var outpassItem = db.OutpassItemTables.FirstOrDefault(oi =>
                            oi.OutpassId == Outpass.OutpassId &&
                            oi.StockLabelId == barcodeItem.StockLabelId);

                        if (outpassItem != null)
                        {
                            // ✅ ENHANCED LOGGING: Debug rack assignment issues
                            Console.WriteLine($"Updating OutpassItem for StockLabelId {barcodeItem.StockLabelId}: " +
                                            $"ReturnedRackId {barcodeItem.ReturnedRackId} (was {outpassItem.ReturnedRackId})");

                            outpassItem.ReturnedQuantity = barcodeItem.ReturnedQuantity ?? barcodeItem.Quantity;
                            outpassItem.ReturnedRackId = barcodeItem.ReturnedRackId ?? barcodeItem.CurrentRackId;
                            outpassItem.ReasonForLessQuantity = barcodeItem.ReasonForLessQuantity ?? "";
                            outpassItem.ReturnCompletedBy = GlobalData.loggedInUser;
                            outpassItem.ReturnCompletedDate = transactionDate;

                            Console.WriteLine($"Updated OutpassItem: ReturnedRackId now {outpassItem.ReturnedRackId}");
                        }
                        else
                        {
                            Console.WriteLine($"WARNING: No OutpassItem found for StockLabelId {barcodeItem.StockLabelId}");
                        }

                        // Handle consumption tracking for partial returns
                        if (barcodeItem.ReturnedQuantity.HasValue && barcodeItem.ReturnedQuantity < barcodeItem.Quantity)
                        {
                            var consumedQuantity = barcodeItem.Quantity - barcodeItem.ReturnedQuantity.Value;
                            //Purpose column support 200 characters max
                            var purpose = $"Returned less qty Via Out Pass No.: {mm.OutpassNumber} with Reason: {barcodeItem.ReasonForLessQuantity}";

                            var stockProduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == barcodeItem.StockProductId);
                            var consumeRecord = new ConsumeStockProductMaster
                            {
                                ProductId = barcodeItem.ProductId,
                                StockProductId = barcodeItem.StockProductId,
                                StockId = stockProduct?.StockId,
                                Quantity = consumedQuantity,
                                Purpose = purpose,
                                ConsumedDate = transactionDate,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = transactionDate,
                                RackId = barcodeItem.CurrentRackId,
                                StoreId = barcodeItem.CurrentStoreId,
                                Unit = barcodeItem.Unit,
                                IsDamaged = false
                            };

                            db.ConsumeStockProductMasters.Add(consumeRecord);
                            db.SaveChanges();

                            stockDataFn.UpdateStockLabelStatus(db, barcodeItem.StockLabelId,
                                StockLabelStatus.Consumed,
                                $"Consumed quantity: {consumedQuantity} {barcodeItem.PackagingUnit} for {purpose}",
                                consumeRecord.ConsumeStockProductId,
                                "ConsumeStockProductMaster");
                        }

                        // Only process items that have a returned quantity
                        if (barcodeItem.ReturnedQuantity.HasValue && barcodeItem.ReturnedQuantity > 0)
                        {
                            // Get the StockProductId from the stock label
                            var stockLabel = db.StockLabelTables.FirstOrDefault(x => x.StockLabelId == barcodeItem.StockLabelId);
                            if (stockLabel?.StockProductId > 0)
                            {
                                var returnedRackId = barcodeItem.ReturnedRackId ?? barcodeItem.CurrentRackId;

                                // Only proceed if we have a valid rack ID
                                if (returnedRackId > 0)
                                {
                                    // 1. Update or create StockProductAllocationTable record
                                    var stockAllocation = db.StockProductAllocationTables.FirstOrDefault(x =>
                                        x.StockProductId == stockLabel.StockProductId &&
                                        x.RackId == returnedRackId &&
                                        x.InspectionType == PmsCommon.PMSStatus.Accepted);

                                    if (stockAllocation == null)
                                    {
                                        // Create new allocation record for the return location
                                        var newAllocation = new StockProductAllocationTable()
                                        {
                                            StockProductId = stockLabel.StockProductId,
                                            Quantity = barcodeItem.ReturnedQuantity.Value,
                                            InspectionType = PmsCommon.PMSStatus.Accepted,
                                            RackId = returnedRackId
                                        };
                                        db.StockProductAllocationTables.Add(newAllocation);

                                        // Save to get the AllocationId
                                        db.SaveChanges();

                                        // Update the stock label's AllocationId to link to this allocation
                                        stockLabel.AllocationId = newAllocation.AllocationId;
                                    }
                                    else
                                    {
                                        // Update existing allocation quantity
                                        stockAllocation.Quantity += barcodeItem.ReturnedQuantity.Value;

                                        // Update the stock label's AllocationId to link to this allocation
                                        stockLabel.AllocationId = stockAllocation.AllocationId;
                                    }

                                    // 2. Update StockProductTable master quantity
                                    var stockProduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == stockLabel.StockProductId);
                                    if (stockProduct != null)
                                    {
                                        stockProduct.Quantity += barcodeItem.ReturnedQuantity.Value;
                                    }

                                    Console.WriteLine($"Updated stock allocation for StockLabelId {barcodeItem.StockLabelId}: " +
                                                    $"Added {barcodeItem.ReturnedQuantity.Value} to RackId {returnedRackId}");
                                }
                            }
                        }
                    }
                }

                // 5. ✅ DIRECT RELATIONSHIP: Handle barcode label reactivation using OutpassItemTable
                var outpassBarcodeItems = db.OutpassItemTables
                    .Where(x => x.OutpassId == Outpass.OutpassId && x.StockLabelId != null)
                    .ToList();

                foreach (var outpassItem in outpassBarcodeItems)
                {
                    // Update stock label status from OutPassDispatched to OutPassReturned and reactivate
                    stockDataFn.UpdateStockLabelStatus(db, outpassItem.StockLabelId.Value,
                        StockLabelStatus.OutPassReturned,
                        $"OutPass Returned - Stock Back in Inventory via OutPass #{Outpass.OutpassId}",
                        Outpass.OutpassId,
                        "OutpassMaster");
                    stockDataFn.UpdateStockLabelStatus(db, outpassItem.StockLabelId.Value,
                        StockLabelStatus.Active,
                        $"Stock Returned Accepted - Status changed to Active",
                        Outpass.OutpassId,
                        "OutpassMaster");

                    // Reactivate the label for future use
                    var stockLabel = db.StockLabelTables.FirstOrDefault(x => x.StockLabelId == outpassItem.StockLabelId.Value);
                    if (stockLabel != null)
                    {
                        stockLabel.IsActive = true;

                        // ✅ SIMPLIFIED: Update location based on OutpassItemTable data directly
                        if (outpassItem.ReturnedRackId > 0)
                        {
                            stockLabel.CurrentRackId = outpassItem.ReturnedRackId;
                            stockLabel.CurrentStoreId = GetStoreIdFromRackId(db, outpassItem.ReturnedRackId.Value);
                            stockLabel.Quantity = outpassItem.ReturnedQuantity;
                        }
                    }
                }

                // 6. ADD STATUS HISTORY (Enhanced)
                db.OutpassStatusHistories.Add(new OutpassStatusHistory
                {
                    OutpassId = Outpass.OutpassId,
                    Status = OutpassStatus.ReturnCompleted,
                    AddedBy = GlobalData.loggedInUser,
                    AddedDate = transactionDate,
                    Remark = "OutPass return completed with consumption tracking"
                });

                // 7. TRANSACTION INTEGRITY - SAVE ALL CHANGES
                db.SaveChanges();
                transaction.Commit();

                return new ApiFunctionResponseVm(HttpStatusCode.OK, "OutPass return completed successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, $"Error processing OutPass return: {ex.Message}");
            }
        }

        // Helper method to get StoreId from RackId
        private long? GetStoreIdFromRackId(pmsdbContext db, long? rackId)
        {
            if (!rackId.HasValue) return null;

            var rack = db.RackMasters.FirstOrDefault(r => r.RackId == rackId.Value);
            return rack?.StoreId;
        }

        public List<OutPassPurposeMasterVm> GetAllOutPassPurposes()
        {
            List<OutPassPurposeMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.OutPassPurposeMasters
                       where a.Disabled != true
                       select new OutPassPurposeMasterVm
                       {
                           PurposeId = a.PurposeId,
                           PurposeName = a.PurposeName,
                           PurposeCode = a.PurposeCode,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                       }).OrderByDescending(x => x.PurposeId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateOutPassPurpose(OutPassPurposeMasterVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                OutPassPurposeMaster res = new OutPassPurposeMaster();
                if (item.PurposeId == 0)
                {
                    res.PurposeName = item.PurposeName;
                    res.AddedBy = GlobalData.loggedInUser;
                    res.AddedDate = System.DateTime.Now;
                    db.OutPassPurposeMasters.Add(res);
                    db.SaveChanges();

                    res.PurposeCode = "OP-" + res.PurposeId;
                }
                else
                {
                    res = db.OutPassPurposeMasters.Where(x => x.PurposeId == item.PurposeId).FirstOrDefault();

                    if (res != null)
                    {
                        res.PurposeName = item.PurposeName;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = res.PurposeId,
                            TableName = "OutPassPurposeMaster",
                            EntityName = "OutPassPurposeMasters",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm DeleteOutPassPurpose(long itemid)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var res = db.OutPassPurposeMasters.Where(x => x.PurposeId == itemid).FirstOrDefault();
                        if (res != null)
                        {
                            res.Disabled = true;
                            res.DisabledBy = GlobalData.loggedInUser;
                            res.DisabledDate = DateTime.Now;
                            db.SaveChanges();

                        }
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "OutPass Purpose Deleted Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
        public ApiFunctionResponseVm OutpassStatusActions(OutpassStatusActionVm actionVm)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var outpass = db.OutpassMasters.FirstOrDefault(x => x.OutpassId == actionVm.OutpassId);
                if (outpass == null)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Outpass not found");
                }
                switch (actionVm.Status)
                {
                    case OutpassStatus.Approved:
                        var approvalDate = DateTime.Now;
                        outpass.Status = OutpassStatus.Approved;
                        var outpassHistory = db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = outpass.OutpassId,
                                Status = OutpassStatus.Approved,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = approvalDate,
                                Remark = actionVm.Remark
                            }
                        );

                        // ✅ DIRECT RELATIONSHIP: Update barcode items status during approval
                        var outpassBarcodeItems = db.OutpassItemTables
                            .Where(x => x.OutpassId == outpass.OutpassId && x.StockLabelId != null)
                            .ToList();

                        // Initialize StockDataFn for proper label lifecycle management
                        var stockDataFn = new StockDataFn(GlobalData);

                        foreach (var outpassItem in outpassBarcodeItems)
                        {
                            // ✅ ENHANCEMENT 2: Update stock label status from Reserved to Dispatched during approval
                            stockDataFn.UpdateStockLabelStatus(db, outpassItem.StockLabelId.Value,
                            StockLabelStatus.OutPassDispatched,
                            $"OutPass Approved - Dispatched via OutPass #{outpass.OutpassId} for {outpass.Purpose}",
                            outpass.OutpassId,
                            "OutpassMaster");
                            stockDataFn.UpdateStockLabelStatus(db, outpassItem.StockLabelId.Value,
                            StockLabelStatus.InActive,
                            "Marked Inactive as part of OutPass Approval",
                            outpass.OutpassId,
                            "OutpassMaster");

                            // Mark label as inactive since it's dispatched via OutPass
                            var stockLabel = db.StockLabelTables.FirstOrDefault(x => x.StockLabelId == outpassItem.StockLabelId.Value);
                            if (stockLabel != null)
                            {
                                stockLabel.IsActive = false;
                            }
                        }

                        var OutpassItems = db.OutpassItemTables.Where(x => x.OutpassId == outpass.OutpassId).ToList();
                        foreach (var op in OutpassItems)
                        {
                            if (op.StockProductId > 0)
                            {
                                var removestock = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == op.StockProductId && x.RackId == op.RackId);
                                if (removestock != null)
                                {
                                    removestock.Quantity = removestock.Quantity - op.Quantity.Value;
                                    if (removestock.Quantity == 0)
                                    {
                                        db.StockProductAllocationTables.Remove(removestock);
                                    }
                                }
                                // ✅ NEW: Update master quantity
                                var stockProduct = db.StockProductTables.FirstOrDefault(x => x.StockProductId == op.StockProductId);
                                if (stockProduct != null)
                                {
                                    stockProduct.Quantity = stockProduct.Quantity - op.Quantity.Value;
                                }
                            }
                        }
                        break;
                    case OutpassStatus.Cancelled:
                        var cancellationDate = DateTime.Now;
                        outpass.Status = OutpassStatus.Cancelled;

                        // ✅ DIRECT RELATIONSHIP: Update barcode items status for cancelled OutPasses
                        var outpassBarcodeItemsForCancellation = db.OutpassItemTables
                            .Where(x => x.OutpassId == outpass.OutpassId && x.StockLabelId != null)
                            .ToList();

                        // Initialize StockDataFn for proper label lifecycle management
                        var stockDataFnCancellation = new StockDataFn(GlobalData);

                        foreach (var outpassItem in outpassBarcodeItemsForCancellation)
                        {
                            // ✅ ENHANCEMENT 4: Update stock label status from Reserved back to Active for cancelled OutPasses
                            stockDataFnCancellation.UpdateStockLabelStatus(db, outpassItem.StockLabelId.Value,
                                StockLabelStatus.Active,
                                $"OutPass Cancelled - Stock Released for Reallocation",
                                outpass.OutpassId,
                                "OutpassMaster");
                        }

                        var gateInMapping = db.GateInInvoiceMappingTables.Where(x => x.OutpassId == outpass.OutpassId).ToList();
                        if (gateInMapping != null && gateInMapping.Count == 1)
                        {
                            var gateIn = db.GateInTables.FirstOrDefault(x => x.GateInId == gateInMapping[0].GateInId);
                            if (gateIn != null)
                            {
                                db.GateInTables.Remove(gateIn);
                            }
                            db.GateInInvoiceMappingTables.RemoveRange(gateInMapping);
                        }

                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = outpass.OutpassId,
                                Status = OutpassStatus.Cancelled,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = cancellationDate,
                                Remark = actionVm.Remark
                            }
                        );
                        break;
                    case OutpassStatus.Rejected:
                        var rejectionDate = DateTime.Now;
                        outpass.Status = OutpassStatus.Rejected;

                        // ✅ DIRECT RELATIONSHIP: Update barcode items status for rejected OutPasses
                        var outpassBarcodeItemsForRejection = db.OutpassItemTables
                            .Where(x => x.OutpassId == outpass.OutpassId && x.StockLabelId != null)
                            .ToList();

                        // Initialize StockDataFn for proper label lifecycle management
                        var stockDataFnRejection = new StockDataFn(GlobalData);

                        foreach (var outpassItem in outpassBarcodeItemsForRejection)
                        {
                            // ✅ ENHANCEMENT 3: Update stock label status from Reserved back to Active for rejected OutPasses
                            stockDataFnRejection.UpdateStockLabelStatus(db, outpassItem.StockLabelId.Value,
                                StockLabelStatus.Active,
                                $"OutPass Rejected - {actionVm.Remark} - Stock Released",
                                outpass.OutpassId,
                                "OutpassMaster");
                        }

                        var gateInMappingR = db.GateInInvoiceMappingTables.Where(x => x.OutpassId == outpass.OutpassId).ToList();
                        if (gateInMappingR != null && gateInMappingR.Count == 1)
                        {
                            var gateIn = db.GateInTables.FirstOrDefault(x => x.GateInId == gateInMappingR[0].GateInId);
                            if (gateIn != null)
                            {
                                db.GateInTables.Remove(gateIn);
                            }
                            db.GateInInvoiceMappingTables.RemoveRange(gateInMappingR);
                        }
                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = outpass.OutpassId,
                                Status = OutpassStatus.Rejected,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = rejectionDate,
                                Remark = actionVm.Remark
                            }
                        );
                        break;
                    case OutpassStatus.OnHold:
                        outpass.Status = OutpassStatus.OnHold;
                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = outpass.OutpassId,
                                Status = OutpassStatus.OnHold,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = actionVm.Remark
                            }
                        );
                        break;
                    default:
                        break;
                }
                db.SaveChanges();
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Outpass Status Updated Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }
        public List<OutpassStatusHistoryVm> GetOutpassTimeline(long outpassId)
        {
            using var db = new pmsdbContext();
            var res = (from a in db.OutpassStatusHistories
                       join b in db.UserMasters on a.AddedBy equals b.Email
                       join c in db.OutpassMasters on a.OutpassId equals c.OutpassId
                       where a.OutpassId == outpassId
                       select new OutpassStatusHistoryVm
                       {
                           OutpassId = a.OutpassId,
                           OutpassNumber = c.OutpassNumber,
                           Status = a.Status,
                           AddedBy = a.AddedBy,
                           AddedByName = b.Name,
                           AddedDate = a.AddedDate,
                           Remark = a.Remark
                       }).ToList();
            return res;
        }

        /// <summary>
        /// Helper method to get outpass items by outpass ID
        /// </summary>
        private List<OutpassItemTableVm> GetOutpassItemsByOutpassId(long outpassId)
        {
            using var db = new pmsdbContext();
            return (from op in db.OutpassItemTables
                    join sp in db.StockProductTables on op.StockProductId equals sp.StockProductId into spd
                    from sp in spd.DefaultIfEmpty()
                    join sm in db.StockMasters on sp.StockId equals sm.StockId into smd
                    from sm in smd.DefaultIfEmpty()
                    join p in db.ProductMasters on sp.ProductId equals p.ProductId into pd
                    from p in pd.DefaultIfEmpty()
                    join r in db.RackMasters on op.RackId equals r.RackId into rd
                    from r in rd.DefaultIfEmpty()
                    join s in db.StoreMasters on r.StoreId equals s.StoreId into sd
                    from s in sd.DefaultIfEmpty()
                    where op.OutpassId == outpassId
                    select new OutpassItemTableVm
                    {
                        OutpassItemId = op.OutpassItemId,
                        OutpassId = op.OutpassId,
                        ProductId = p.ProductId,
                        ProductName = op.ProductName,
                        StockProductId = op.StockProductId,
                        RackId = op.RackId,
                        Quantity = op.Quantity,
                        Amount = op.Amount,
                        Unit = op.Unit,
                        RackName = r.RackName,
                        StoreName = s.StoreName,
                        BatchNo = sm.Batch,
                        ReturnedQuantity = op.ReturnedQuantity,
                        ReturnedRackId = op.ReturnedRackId
                    }).ToList();
        }

        /// <summary>
        /// Get all pending returnable outpasses for notification purposes
        /// </summary>
        public List<PendingReturnableOutPassVm> GetPendingReturnableOutPasses(PendingReturnableOutPassRequestVm request)
        {
            using var db = new pmsdbContext();
            var currentDate = DateTime.UtcNow;

            var query = from outpass in db.OutpassMasters
                        join user in db.UserMasters on outpass.AddedBy equals user.Email
                        where outpass.OutpassType == "Returnable"
                              && outpass.IsOutpassIn != true
                              && outpass.Status == OutpassStatus.Approved
                        select new
                        {
                            outpass,
                            user.Name
                        };

            var outpassData = query.ToList();
            var result = new List<PendingReturnableOutPassVm>();

            foreach (var item in outpassData)
            {
                var outpassItems = GetOutpassItemsByOutpassId(item.outpass.OutpassId);
                var totalAmount = outpassItems.Sum(x => (x.Quantity ?? 0) * (x.Amount ?? 0));

                var daysOverdue = 0;
                var isOverdue = false;

                if (item.outpass.ExpectedReturnDate.HasValue)
                {
                    var daysDiff = (currentDate - item.outpass.ExpectedReturnDate.Value).Days;
                    if (daysDiff > 0)
                    {
                        daysOverdue = daysDiff;
                        isOverdue = true;
                    }
                }

                // Apply filters
                if (request.IncludeOverdueOnly && !isOverdue)
                    continue;

                if (request.DaysOverdueThreshold.HasValue && daysOverdue < request.DaysOverdueThreshold.Value)
                    continue;

                var pendingOutpass = new PendingReturnableOutPassVm
                {
                    OutpassId = item.outpass.OutpassId,
                    OutpassNumber = item.outpass.OutpassNumber,
                    OutpassTo = item.outpass.OutpassTo,
                    OutpassDate = item.outpass.OutpassDate,
                    ExpectedReturnDate = item.outpass.ExpectedReturnDate,
                    Purpose = item.outpass.Purpose,
                    Remark = item.outpass.Remark,
                    AddedBy = item.Name,
                    AddedDate = item.outpass.AddedDate,
                    OutpassItems = outpassItems,
                    DaysOverdue = daysOverdue,
                    IsOverdue = isOverdue,
                    Status = item.outpass.Status,
                    TotalAmount = totalAmount
                };

                result.Add(pendingOutpass);
            }

            return result.OrderByDescending(x => x.DaysOverdue).ThenBy(x => x.ExpectedReturnDate).ToList();
        }

        /// <summary>
        /// Get pending returnable outpass report data
        /// </summary>
        public PendingReturnableOutPassReportVm GetPendingReturnableOutPassReportData(PendingReturnableOutPassRequestVm request)
        {
            var pendingOutPasses = GetPendingReturnableOutPasses(request);

            return new PendingReturnableOutPassReportVm
            {
                PendingOutPasses = pendingOutPasses,
                TotalPendingCount = pendingOutPasses.Count,
                OverdueCount = pendingOutPasses.Count(x => x.IsOverdue),
                GeneratedDate = request.GeneratedDate ?? DateTime.UtcNow,
                TotalPendingAmount = pendingOutPasses.Sum(x => x.TotalAmount)
            };
        }

        /// <summary>
        /// Get single pending returnable outpass by ID for reminder notification
        /// </summary>
        public PendingReturnableOutPassVm GetPendingReturnableOutPassById(long outpassId)
        {
            var request = new PendingReturnableOutPassRequestVm { GeneratedDate = DateTime.UtcNow };
            var allPending = GetPendingReturnableOutPasses(request);
            return allPending.FirstOrDefault(x => x.OutpassId == outpassId);
        }

        /// <summary>
        /// Generate PDF for OutPass
        /// </summary>
        public ApiFunctionResponseVm GetOutPassPdf(long outpassId, IPdfService pdfService)
        {
            try
            {
                using (var db = new pmsdbContext())
                {
                    var outpass = GetOutpassById(outpassId);
                    if (outpass == null)
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "OutPass not found");

                    var pdfData = new OutPassPdfAdapter(outpass);

                    var currentDate = DateTime.UtcNow.ToString("yyyy/MM/dd");
                    var formattedOutpassNumber = outpass.OutpassNumber.Replace("/", "-");
                    var fileName = $"{currentDate}/OutPass_{formattedOutpassNumber}.pdf";

                    var pdfUrl = pdfService.GeneratePdfAndUploadToStorageAsync(
                        pdfData,
                        fileName
                    );

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, pdfUrl.Result);
                }
            }
            catch (Exception ex)
            {
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError,
                    $"Error generating OutPass PDF: {ex.Message}");
            }
        }

        /// <summary>
        /// Get OutPass print data with consolidated barcode items and unit grouping
        /// </summary>
        public OutpassPrintDataVm GetOutpassPrintData(long outpassId)
        {
            using (var db = new pmsdbContext())
            {
                var outpass = GetOutpassById(outpassId);
                if (outpass == null)
                    return null;

                var printData = new OutpassPrintDataVm
                {
                    OutpassDetails = outpass,
                    UnitGroups = new List<PrintUnitGroupVm>(),
                    Summary = new PrintSummaryVm()
                };

                // Create a consolidated list of all items (manual + barcode)
                var allItems = new List<PrintItemVm>();

                // Process manual items
                foreach (var item in outpass.OutpassItems)
                {
                    allItems.Add(new PrintItemVm
                    {
                        ProductId = item.ProductId ?? 0,
                        ProductName = item.ProductName,
                        BatchNo = item.BatchNo,
                        Quantity = item.Quantity ?? 0,
                        Unit = item.Unit,
                        Amount = item.Amount ?? 0,
                        TotalAmount = (item.Quantity ?? 0) * (item.Amount ?? 0),
                        EntryType = "Manual",
                        StoreName = item.StoreName,
                        RackName = item.RackName,
                        SerialNumbers = new List<string>(),
                        PackagingUnits = new List<string>()
                    });
                }

                // Process and consolidate barcode items by ProductId
                var barcodeGroups = outpass.BarcodeItems
                    .GroupBy(b => new { b.ProductId, b.ProductName, b.Unit })
                    .ToList();

                foreach (var group in barcodeGroups)
                {
                    var barcodeItems = group.ToList();
                    var totalQuantity = barcodeItems.Sum(b => b.Quantity);
                    var totalAmount = barcodeItems.Sum(b => b.Amount * b.Quantity);

                    // Collect serial numbers and packaging units
                    var serialNumbers = barcodeItems.Select(b => b.SerialNo).Where(s => !string.IsNullOrEmpty(s)).ToList();
                    var packagingUnits = barcodeItems.Select(b => b.PackagingUnit).Where(p => !string.IsNullOrEmpty(p)).ToList();

                    // Create packaging breakdown (e.g., "3 Rolls, 2 Bags")
                    var packagingBreakdown = CreatePackagingBreakdown(packagingUnits);

                    allItems.Add(new PrintItemVm
                    {
                        ProductId = group.Key.ProductId,
                        ProductName = group.Key.ProductName,
                        BatchNo = barcodeItems.FirstOrDefault()?.BatchNo ?? "",
                        Quantity = totalQuantity,
                        Unit = group.Key.Unit,
                        Amount = barcodeItems.Average(b => b.Amount), // Average unit price
                        TotalAmount = totalAmount,
                        EntryType = "Barcode",
                        StoreName = barcodeItems.FirstOrDefault()?.StoreName ?? "",
                        RackName = barcodeItems.FirstOrDefault()?.RackName ?? "",
                        SerialNumbers = serialNumbers,
                        PackagingUnits = packagingUnits,
                        PackagingBreakdown = packagingBreakdown,
                        LabelCount = barcodeItems.Count
                    });
                }

                // Group all items by unit type
                var unitGroups = allItems
                    .GroupBy(item => string.IsNullOrEmpty(item.Unit) ? "N/A" : item.Unit)
                    .ToList();

                foreach (var unitGroup in unitGroups)
                {
                    var items = unitGroup.ToList();
                    var unitType = unitGroup.Key;

                    // Further group by ProductId within each unit group
                    var productGroups = items
                        .GroupBy(item => new { item.ProductId, item.ProductName })
                        .Select(pg => new PrintProductVm
                        {
                            ProductId = pg.Key.ProductId,
                            ProductName = pg.Key.ProductName,
                            Items = pg.ToList(),
                            TotalQuantity = pg.Sum(i => i.Quantity),
                            TotalAmount = pg.Sum(i => i.TotalAmount),
                            LabelCount = pg.Sum(i => i.LabelCount),
                            PackagingBreakdown = CreatePackagingBreakdown(pg.SelectMany(i => i.PackagingUnits).ToList()),
                            HasBarcode = pg.Any(i => i.EntryType == "Barcode"),
                            HasManual = pg.Any(i => i.EntryType == "Manual")
                        })
                        .ToList();

                    printData.UnitGroups.Add(new PrintUnitGroupVm
                    {
                        UnitType = unitType,
                        Products = productGroups,
                        TotalQuantity = items.Sum(i => i.Quantity),
                        TotalAmount = items.Sum(i => i.TotalAmount),
                        TotalLabels = items.Sum(i => i.LabelCount),
                        PackagingBreakdown = CreatePackagingBreakdown(items.SelectMany(i => i.PackagingUnits).ToList())
                    });
                }

                // Calculate summary
                printData.Summary = new PrintSummaryVm
                {
                    TotalProducts = allItems.GroupBy(i => i.ProductId).Count(),
                    TotalManualItems = outpass.OutpassItems.Count,
                    TotalBarcodeItems = outpass.BarcodeItems.Count,
                    TotalLabels = allItems.Sum(i => i.LabelCount),
                    GrandTotalQuantity = allItems.Sum(i => i.Quantity),
                    GrandTotalAmount = allItems.Sum(i => i.TotalAmount),
                    UnitsUsed = printData.UnitGroups.Select(ug => ug.UnitType).Distinct().ToList(),
                    OverallPackagingBreakdown = CreatePackagingBreakdown(allItems.SelectMany(i => i.PackagingUnits).ToList())
                };

                return printData;
            }
        }

        /// <summary>
        /// Create packaging breakdown string from list of packaging units
        /// </summary>
        private string CreatePackagingBreakdown(List<string> packagingUnits)
        {
            if (packagingUnits == null || !packagingUnits.Any())
                return "";

            var breakdown = packagingUnits
                .Where(p => !string.IsNullOrEmpty(p))
                .GroupBy(p => p)
                .Select(g => $"{g.Count()} {g.Key}")
                .ToList();

            return string.Join(", ", breakdown);
        }

        #region Barcode Functionality

        /// <summary>
        /// Add Out Pass with barcode items only
        /// </summary>
        private ApiFunctionResponseVm AddBarcodeOutpass(OutpassMasterVm Outpass)
        {
            using (var db = new pmsdbContext())
            using (var transaction = db.Database.BeginTransaction())
            {
                try
                {
                    var transactionDate = DateTime.Now;
                    // Validate barcode labels
                    if (Outpass.BarcodeItems == null || !Outpass.BarcodeItems.Any())
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "No barcode items provided for barcode Out Pass.");
                    }

                    var stockLabelIds = Outpass.BarcodeItems.Select(bi => bi.StockLabelId).ToList();
                    var stockLabels = db.StockLabelTables
                        .Where(sl => stockLabelIds.Contains(sl.StockLabelId))
                        .ToList();

                    // Validate all labels exist and are accepted
                    if (stockLabels.Count != stockLabelIds.Count)
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Some barcode labels were not found.");
                    }

                    if (stockLabels.Any(sl => sl.InspectionStatus != PMSStatus.Accepted))
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Some barcode labels are not accepted. Please ensure all labels are inspected and accepted.");
                    }

                    // Create OutpassMaster
                    var outpassMaster = new OutpassMaster
                    {
                        OutpassTo = Outpass.OutpassTo,
                        OutpassDate = Outpass.OutpassDate,
                        OutpassType = Outpass.OutpassType,
                        Purpose = Outpass.Purpose,
                        PurposeId = Outpass.PurposeId,
                        Remark = Outpass.Remark,
                        IsOutpassIn = false,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = transactionDate,
                        OutpassToCustomerId = Outpass.OutpassToCustomerId,
                        ExpectedReturnDate = Outpass.ExpectedReturnDate,
                        Status = OutpassStatus.ApprovalPending,
                        TransportId = Outpass.TransportId,
                        VehicleId = Outpass.VehicleId,
                        IsGateIn = Outpass.VehicleId > 0,
                        CreateMode = "Barcode"
                    };

                    db.OutpassMasters.Add(outpassMaster);
                    db.SaveChanges();

                    // Generate OutPass number
                    outpassMaster.OutpassNumber = "Z-DC/" + outpassMaster.OutpassId;
                    db.SaveChanges();

                    // Handle Gate In if vehicle is specified
                    if (Outpass.VehicleId > 0)
                    {
                        HandleGateInForOutpass(db, Outpass.VehicleId.Value, outpassMaster.OutpassId);
                    }

                    // Add status history
                    db.OutpassStatusHistories.Add(new OutpassStatusHistory
                    {
                        OutpassId = outpassMaster.OutpassId,
                        Status = OutpassStatus.ApprovalPending,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = transactionDate,
                        Remark = Outpass.Remark
                    });

                    // Create OutpassItemTable entries for barcode items
                    foreach (var barcodeItem in Outpass.BarcodeItems)
                    {
                        var stockLabel = stockLabels.First(sl => sl.StockLabelId == barcodeItem.StockLabelId);

                        var outpassItem = new OutpassItemTable
                        {
                            OutpassId = outpassMaster.OutpassId,
                            StockProductId = stockLabel.StockProductId,
                            ProductName = barcodeItem.ProductName,
                            Quantity = stockLabel.Quantity,
                            Amount = barcodeItem.Amount,
                            Unit = barcodeItem.Unit,
                            StockLabelId = barcodeItem.StockLabelId // ✅ CRITICAL FIX: Store StockLabelId for direct relationship
                        };
                        db.OutpassItemTables.Add(outpassItem);

                        // ✅ ENHANCEMENT 1: Update stock label status to Reserved during OutPass creation
                        var stockDataFn = new StockDataFn(GlobalData);
                        stockDataFn.UpdateStockLabelStatus(db, barcodeItem.StockLabelId,
                            StockLabelStatus.OutPassReserved,
                            $"Reserved for OutPass #{outpassMaster.OutpassId} - Pending Approval",
                            outpassMaster.OutpassId,
                            "OutpassMaster");
                    }

                    db.SaveChanges();
                    transaction.Commit();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Barcode Out Pass created successfully. Stock will be removed upon approval.");
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    throw;
                }
            }
        }

        /// <summary>
        /// Add Out Pass with both manual and barcode items
        /// </summary>
        private ApiFunctionResponseVm AddMixedModeOutpass(OutpassMasterVm Outpass)
        {
            using (var db = new pmsdbContext())
            using (var transaction = db.Database.BeginTransaction())
            {
                try
                {
                    var transactionDate = DateTime.Now;
                    // Validate that we have both types of items
                    bool hasManualItems = Outpass.OutpassItems != null && Outpass.OutpassItems.Any();
                    bool hasBarcodeItems = Outpass.BarcodeItems != null && Outpass.BarcodeItems.Any();

                    if (!hasManualItems && !hasBarcodeItems)
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "No items provided for Out Pass.");
                    }

                    // Validate barcode items if present
                    if (hasBarcodeItems)
                    {
                        var stockLabelIds = Outpass.BarcodeItems.Select(bi => bi.StockLabelId).ToList();
                        var stockLabels = db.StockLabelTables
                            .Where(sl => stockLabelIds.Contains(sl.StockLabelId))
                            .ToList();

                        if (stockLabels.Any(sl => sl.InspectionStatus != PMSStatus.Accepted))
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Some barcode labels are not accepted.");
                        }
                    }

                    // Create OutpassMaster
                    var outpassMaster = new OutpassMaster
                    {
                        OutpassTo = Outpass.OutpassTo,
                        OutpassDate = Outpass.OutpassDate,
                        OutpassType = Outpass.OutpassType,
                        Purpose = Outpass.Purpose,
                        PurposeId = Outpass.PurposeId,
                        Remark = Outpass.Remark,
                        IsOutpassIn = false,
                        AddedBy = Outpass.AddedBy,
                        AddedDate = transactionDate,
                        OutpassToCustomerId = Outpass.OutpassToCustomerId,
                        ExpectedReturnDate = Outpass.ExpectedReturnDate,
                        Status = OutpassStatus.ApprovalPending,
                        TransportId = Outpass.TransportId,
                        VehicleId = Outpass.VehicleId,
                        IsGateIn = Outpass.VehicleId > 0,
                        CreateMode = "Mixed"
                    };

                    db.OutpassMasters.Add(outpassMaster);
                    db.SaveChanges();

                    outpassMaster.OutpassNumber = "Z-DC/" + outpassMaster.OutpassId;
                    db.SaveChanges();

                    if (Outpass.VehicleId > 0)
                    {
                        HandleGateInForOutpass(db, Outpass.VehicleId.Value, outpassMaster.OutpassId);
                    }

                    db.OutpassStatusHistories.Add(new OutpassStatusHistory
                    {
                        OutpassId = outpassMaster.OutpassId,
                        Status = OutpassStatus.ApprovalPending,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = transactionDate,
                        Remark = Outpass.Remark
                    });

                    // Add manual items
                    if (hasManualItems)
                    {
                        foreach (var item in Outpass.OutpassItems)
                        {
                            var outpassItem = new OutpassItemTable
                            {
                                OutpassId = outpassMaster.OutpassId,
                                StockProductId = item.StockProductId,
                                ProductName = item.ProductName,
                                Quantity = item.Quantity,
                                Amount = item.Amount,
                                Unit = item.Unit,
                                RackId = item.RackId
                            };
                            db.OutpassItemTables.Add(outpassItem);
                        }
                    }

                    // Add barcode items
                    if (hasBarcodeItems)
                    {
                        var stockLabelIds = Outpass.BarcodeItems.Select(bi => bi.StockLabelId).ToList();
                        var stockLabels = db.StockLabelTables
                            .Where(sl => stockLabelIds.Contains(sl.StockLabelId))
                            .ToList();

                        foreach (var barcodeItem in Outpass.BarcodeItems)
                        {
                            var stockLabel = stockLabels.First(sl => sl.StockLabelId == barcodeItem.StockLabelId);

                            var outpassItem = new OutpassItemTable
                            {
                                OutpassId = outpassMaster.OutpassId,
                                StockProductId = stockLabel.StockProductId,
                                ProductName = barcodeItem.ProductName,
                                Quantity = stockLabel.Quantity,
                                Amount = barcodeItem.Amount,
                                Unit = barcodeItem.Unit,
                                StockLabelId = barcodeItem.StockLabelId // ✅ CRITICAL FIX: Store StockLabelId for direct relationship
                            };
                            db.OutpassItemTables.Add(outpassItem);

                            // ✅ ENHANCEMENT 1: Update stock label status to Reserved during OutPass creation
                            var stockDataFn = new StockDataFn(GlobalData);
                            stockDataFn.UpdateStockLabelStatus(db, barcodeItem.StockLabelId,
                                StockLabelStatus.OutPassReserved,
                                $"Reserved for OutPass #{outpassMaster.OutpassId} - Pending Approval",
                                outpassMaster.OutpassId,
                                "OutpassMaster");
                        }
                    }

                    db.SaveChanges();
                    transaction.Commit();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Mixed mode Out Pass created successfully.");
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    throw;
                }
            }
        }

        /// <summary>
        /// Helper method to handle Gate In for Out Pass
        /// </summary>
        private void HandleGateInForOutpass(pmsdbContext db, long vehicleId, long outpassId)
        {
            GateInTable sm = db.GateInTables.FirstOrDefault(x => x.VehicleId == vehicleId && x.GateIn == true && x.GateOut != true);
            if (sm == null)
            {
                sm = new GateInTable();
                sm.VehicleId = vehicleId;
                sm.GateInDate = DateTime.Now;
                sm.GateInPerson = "System";
                sm.GateInPersonContact = "System";
                sm.GateIn = true;
                sm.AddedBy = GlobalData.loggedInUser;
                sm.AddedDate = DateTime.Now;
                sm.Type = "Outpass Dispatch";
                db.GateInTables.Add(sm);
                db.SaveChanges();
            }
            GateInInvoiceMappingTable gi = new()
            {
                OutpassId = outpassId,
                GateInId = sm.GateInId
            };
            db.GateInInvoiceMappingTables.Add(gi);
            db.SaveChanges();
        }

        /// <summary>
        /// Validate if a batch has barcode labels
        /// </summary>
        public BarcodeValidationResult ValidateBarcodeLabelsForBatch(long stockProductId)
        {
            using (var db = new pmsdbContext())
            {
                var barcodeLabels = db.StockLabelTables
                    .Where(sl => sl.StockProductId == stockProductId && sl.IsActive == true)
                    .Select(sl => new StockLabelTableVm
                    {
                        StockLabelId = sl.StockLabelId,
                        SerialNo = sl.SerialNo,
                        Quantity = sl.Quantity ?? 0,
                        PackagingUnit = sl.PackagingUnit,
                        ProductId = sl.ProductId,
                        StockProductId = sl.StockProductId,
                        CurrentStoreId = sl.CurrentStoreId ?? 0,
                        CurrentRackId = sl.CurrentRackId ?? 0,
                        InspectionStatus = sl.InspectionStatus,
                        LabelStatus = sl.LabelStatus
                    })
                    .ToList();

                return new BarcodeValidationResult
                {
                    HasBarcodeLabels = barcodeLabels.Any(),
                    BarcodeCount = barcodeLabels.Count,
                    BarcodeLabels = barcodeLabels,
                    Message = barcodeLabels.Any()
                        ? $"This batch has {barcodeLabels.Count} barcode labels generated. Please use barcode scanning method instead."
                        : "No barcode labels found for this batch. Manual entry is allowed."
                };
            }
        }

        #endregion

    }
}
