using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class StoreDataFn
    {
        public List<StoreMasterVm> GetAllStores()
        {
            List<StoreMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.StoreMasters
                       join i in db.BranchMasters on a.BranchId equals i.BranchId into spc
                       from i in spc.DefaultIfEmpty()
                       join d in db.DeptMasters on a.DeptId equals d.DeptId
                       select new StoreMasterVm
                       {
                           StoreId = a.StoreId,
                           DeptId = a.DeptId,
                           DeptCode = d.DeptCode,
                           BranchId = a.BranchId,
                           BranchCode = i.BranchCode,
                           BranchName = i.BranchName,
                           StoreName = a.StoreName,
                           StoreCode = a.StoreCode,
                           StoreDesc = a.StoreDesc,
                           StoreAddedBy = a.StoreAddedBy,
                           StoreAddedDate = a.StoreAddedDate,
                           IsWorkInProgressStore = a.IsWorkInProgressStore
                       }).OrderBy(x => x.StoreCode).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateStore(StoreMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.StoreId == 0)
                {
                    var rec = db.StoreMasters.Where(x => x.StoreCode == br.StoreCode).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                StoreMaster res = new StoreMaster();
                if (br.StoreId == 0)
                {
                    res.DeptId = br.DeptId;
                    res.BranchId = br.BranchId;
                    res.StoreName = br.StoreName;
                    res.StoreCode = br.StoreCode;
                    res.StoreDesc = br.StoreDesc;
                    res.StoreAddedBy = br.StoreAddedBy;
                    res.StoreAddedDate = System.DateTime.Now;
                    res.IsWorkInProgressStore = br.IsWorkInProgressStore;
                    db.StoreMasters.Add(res);
                    db.SaveChanges();

                    RackMaster rack = new RackMaster();
                    rack.RackName = br.StoreName + " Rack";
                    rack.StoreId = res.StoreId;
                    rack.RackCode = br.StoreCode + "_Rack";
                    rack.RackAddedBy = br.StoreAddedBy;
                    rack.RackAddedDate = System.DateTime.Now;
                    db.RackMasters.Add(rack);
                    db.SaveChanges();
                }
                else
                {
                    res = db.StoreMasters.Where(x => x.StoreId == br.StoreId).FirstOrDefault();
                    if (res != null)
                    {
                        res.DeptId = br.DeptId;
                        res.BranchId = br.BranchId;
                        res.StoreName = br.StoreName;
                        res.StoreCode = br.StoreCode;
                        res.StoreDesc = br.StoreDesc;
                        res.StoreAddedBy = br.StoreAddedBy;
                        res.StoreAddedDate = System.DateTime.Now;
                        res.IsWorkInProgressStore = br.IsWorkInProgressStore;
                        db.SaveChanges();
                    }
                }
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
    }
}
