using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class RackDataFn
    {
        public List<RackMasterVm> GetAllRacks()
        {
            List<RackMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.RackMasters
                       join i in db.StoreMasters on a.StoreId equals i.StoreId
                       select new RackMasterVm
                       {
                           RackId = a.RackId,
                           StoreId = a.StoreId,
                           StoreName = i.StoreName,
                           RackName = a.RackName,
                           RackCode = a.RackCode,
                           RackDesc = a.RackDesc,
                           RackAddedBy = a.RackAddedBy,
                           RackAddedDate = a.RackAddedDate
                       }).OrderBy(x => x.RackCode).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateRack(RackMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.RackId == 0)
                {
                    var rec = db.RackMasters.Where(x => x.RackCode == br.RackCode).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                RackMaster res = new RackMaster();
                if (br.RackId == 0)
                {
                    res.RackName = br.RackName;
                    res.StoreId = br.StoreId;
                    res.RackCode = br.RackCode;
                    res.RackDesc = br.RackDesc;
                    res.RackAddedBy = br.RackAddedBy;
                    res.RackAddedDate = System.DateTime.Now;
                    db.RackMasters.Add(res);
                }
                else
                {
                    res = db.RackMasters.Where(x => x.RackId == br.RackId).FirstOrDefault();
                    if (res != null)
                    {
                        res.RackName = br.RackName;
                        res.StoreId = br.StoreId;
                        res.RackCode = br.RackCode;
                        res.RackDesc = br.RackDesc;
                        res.RackAddedBy = br.RackAddedBy;
                        res.RackAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public List<RackMasterVm> GetAllRacksByStoreId(long storeId)
        {
            List<RackMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.RackMasters
                       join i in db.StoreMasters on a.StoreId equals i.StoreId
                       where i.StoreId == storeId
                       select new RackMasterVm
                       {
                           RackId = a.RackId,
                           StoreId = a.StoreId,
                           StoreName = i.StoreName,
                           RackName = a.RackName,
                           RackCode = a.RackCode,
                           RackDesc = a.RackDesc,
                           RackAddedBy = a.RackAddedBy,
                           RackAddedDate = a.RackAddedDate
                       }).OrderBy(x => x.RackId).ToList();
            }
            return res;
        }
    }
}
