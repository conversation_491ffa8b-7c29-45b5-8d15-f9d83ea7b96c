using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class RackDataFn
    {
        public List<RackMasterVm> GetAllRacks()
        {
            List<RackMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.RackMasters
                       join i in db.StoreMasters on a.StoreId equals i.StoreId
                       select new RackMasterVm
                       {
                           RackId = a.RackId,
                           StoreId = a.StoreId,
                           StoreName = i.StoreName,
                           RackName = a.RackName,
                           RackCode = a.RackCode,
                           RackDesc = a.RackDesc,
                           RackAddedBy = a.RackAddedBy,
                           RackAddedDate = a.RackAddedDate
                       }).OrderBy(x => x.RackCode).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateRack(RackMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (br.RackId == 0)
                {
                    var rec = db.RackMasters.Where(x => x.RackCode == br.RackCode).FirstOrDefault();
                    if (rec != null)
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, rec);
                }
                RackMaster res = new RackMaster();
                if (br.RackId == 0)
                {
                    res.RackName = br.RackName;
                    res.StoreId = br.StoreId;
                    res.RackCode = GenerateUniqueRackCode(br.StoreId.Value);
                    res.RackDesc = br.RackDesc;
                    res.RackAddedBy = br.RackAddedBy;
                    res.RackAddedDate = System.DateTime.Now;
                    db.RackMasters.Add(res);
                }
                else
                {
                    res = db.RackMasters.Where(x => x.RackId == br.RackId).FirstOrDefault();
                    if (res != null)
                    {
                        res.RackName = br.RackName;
                        res.StoreId = br.StoreId;
                        res.RackDesc = br.RackDesc;
                        res.RackAddedBy = br.RackAddedBy;
                        res.RackAddedDate = System.DateTime.Now;
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }
        public List<RackMasterVm> GetAllRacksByStoreId(long storeId)
        {
            List<RackMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.RackMasters
                       join i in db.StoreMasters on a.StoreId equals i.StoreId
                       where i.StoreId == storeId
                       select new RackMasterVm
                       {
                           RackId = a.RackId,
                           StoreId = a.StoreId,
                           StoreName = i.StoreName,
                           RackName = a.RackName,
                           RackCode = a.RackCode,
                           RackDesc = a.RackDesc,
                           RackAddedBy = a.RackAddedBy,
                           RackAddedDate = a.RackAddedDate
                       }).OrderBy(x => x.RackId).ToList();
            }
            return res;
        }

        /// <summary>
        /// Generates a unique rack code for a given store.
        /// The code is formed by taking the first letter of each word in the store name
        /// followed by a sequence number (e.g., "ABC-01", "ABC-02").
        /// </summary>
        /// <param name="storeId">The ID of the store for which to generate the rack code</param>
        /// <returns>A unique rack code string, or null if store not found</returns>
        public string GenerateUniqueRackCode(long storeId)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Get the store information
                var store = db.StoreMasters.Where(x => x.StoreId == storeId).FirstOrDefault();
                if (store == null || string.IsNullOrWhiteSpace(store.StoreName))
                {
                    return null;
                }

                // Generate the prefix from store name
                string prefix = GenerateStorePrefix(store.StoreName);
                if (string.IsNullOrEmpty(prefix))
                {
                    return null;
                }

                // Get existing rack codes for this store that start with the prefix
                var existingCodes = db.RackMasters
                    .Where(x => x.StoreId == storeId && x.RackCode.StartsWith(prefix + "R-"))
                    .Select(x => x.RackCode)
                    .ToList();

                // Find the next available sequence number
                int nextSequence = 1;
                string newRackCode;

                do
                {
                    newRackCode = $"{prefix}R-{nextSequence:D3}";
                    nextSequence++;
                } while (existingCodes.Contains(newRackCode));

                return newRackCode;
            }
        }

        /// <summary>
        /// Generates a prefix from store name by taking the first letter of each word.
        /// Handles multiple spaces and special characters.
        /// </summary>
        /// <param name="storeName">The store name to process</param>
        /// <returns>A prefix string made from first letters of each word</returns>
        private string GenerateStorePrefix(string storeName)
        {
            if (string.IsNullOrWhiteSpace(storeName))
            {
                return string.Empty;
            }

            // Split by spaces and filter out empty entries
            var words = storeName.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

            if (words.Length == 0)
            {
                return string.Empty;
            }

            var prefix = new StringBuilder();
            foreach (var word in words)
            {
                if (!string.IsNullOrEmpty(word))
                {
                    // Take the first alphabetic character from each word
                    char firstChar = word.FirstOrDefault(c => char.IsLetter(c));
                    if (firstChar != default(char))
                    {
                        prefix.Append(char.ToUpper(firstChar));
                    }
                }
            }

            return prefix.ToString();
        }
    }
}
