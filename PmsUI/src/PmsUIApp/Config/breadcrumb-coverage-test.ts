/**
 * Breadcrumb Coverage Test
 * This script tests whether all routes from Home HTML have breadcrumb definitions
 */

import { RouteConfigService } from '../Services/route-config.service';
import { ROUTES } from './route-config';

// All routes found in Home.component.html
const HOME_HTML_ROUTES = [
  // Dashboard routes
  '/home/<USER>',
  '/home/<USER>/reporting',
  
  // Sales routes
  '/home/<USER>/add',
  '/home/<USER>/list',
  '/home/<USER>/:type/:id',
  '/home/<USER>/edit/:id',
  '/home/<USER>/proformaInvoice/add',
  '/home/<USER>/proformaInvoice/list',
  '/home/<USER>/proformaInvoice/:type/:id',
  '/home/<USER>/:id',
  
  // Demand routes
  '/home/<USER>/demandlist',
  
  // Purchase Order routes
  '/home/<USER>/add',
  '/home/<USER>/list',
  '/home/<USER>/:type/:id',
  '/home/<USER>/invoicelist',
  '/home/<USER>/:id',
  '/home/<USER>/:id/:type',
  '/home/<USER>/:id',
  
  // Production routes
  '/home/<USER>/formulationcode/add',
  '/home/<USER>/formulationcode/list',
  '/home/<USER>/formulationcode/edit/:id',
  '/home/<USER>/formulationprint/:id',
  '/home/<USER>/pigmentmb/add',
  '/home/<USER>/mixing/add',
  '/home/<USER>/mixing/list',
  '/home/<USER>/mixing/add/:id',
  '/home/<USER>/mixingprint/:id',
  '/home/<USER>/workplan',
  '/home/<USER>/start',
  '/home/<USER>/jumbo/add',
  '/home/<USER>/jumbo/list',
  '/home/<USER>/:id/:JumboId',
  '/home/<USER>',
  '/home/<USER>/postprocess',
  '/home/<USER>/postprocess/lamination',
  '/home/<USER>/processprint/:id',
  '/home/<USER>/finalInspection/add',
  '/home/<USER>/finalInspection/add/:JumboNo',
  '/home/<USER>/finalInspection/list',
  '/home/<USER>/:id/:JumboId/:InspectionId',
  '/home/<USER>/:id/:JumboId',
  '/home/<USER>/:id',
  '/home/<USER>/downtime',
  '/home/<USER>/ActivityLog',
  
  // Dispatch routes
  '/home/<USER>/packaging/add',
  '/home/<USER>/packaging/list',
  '/home/<USER>/packaging/:type/:id',
  '/home/<USER>/:id',
  '/home/<USER>/:id',
  
  // Issue routes
  '/home/<USER>/add',
  '/home/<USER>/list',
  '/home/<USER>/:id',
  
  // Consumption routes
  '/home/<USER>/add',
  '/home/<USER>/add/:workplanid/:saleorderid/:consumedorder',
  '/home/<USER>/list',
  '/home/<USER>/pendingorders',
  
  // Costing routes
  '/home/<USER>/add',
  '/home/<USER>/list',
  '/home/<USER>',
  '/home/<USER>/estimation/add',
  '/home/<USER>/estimation/add/:id',
  '/home/<USER>/estimation/list',
  '/home/<USER>',
  '/home/<USER>/:id',
  '/home/<USER>/:id/:type',
  '/home/<USER>/overhead',
  
  // Inventory routes
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>/:id',
  '/home/<USER>/:id',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>/list',
  '/home/<USER>',
  '/home/<USER>/:id',
  '/home/<USER>',
  '/home/<USER>/:id',
  '/home/<USER>',
  '/home/<USER>/:id',
  '/home/<USER>',
  '/home/<USER>/:id',
  
  // Gate routes
  '/home/<USER>',
  '/home/<USER>',
  
  // Gate Pass routes
  '/home/<USER>',
  '/home/<USER>/:id',
  
  // Out Pass routes
  '/home/<USER>/add',
  '/home/<USER>/list',
  '/home/<USER>/print/:id',
  '/home/<USER>/packinglistprint/:id',
  
  // Quick Tools routes
  '/home/<USER>/measurementconversion-new',
  '/home/<USER>/barcodescanner',
  
  // Barcode routes
  '/home/<USER>/:StockProductId/:StockId/:ProductId/:NumberOfLabels/:action',
  '/home/<USER>/:action',
  
  // IoT Devices routes
  '/home/<USER>',
  
  // Post Process routes
  '/home/<USER>/postprocessprint',
  '/home/<USER>/embossinglist',
  '/home/<USER>/vaccumlist',
  '/home/<USER>/tumblinglist',
  '/home/<USER>/lacquerlist',
  
  // Transport routes
  '/home/<USER>',
  
  // Supplier routes
  '/home/<USER>',
  
  // Customer routes
  '/home/<USER>',
  
  // Product routes
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>/producttransfer',
  
  // Master routes
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>/downtimereason',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  
  // Notification routes
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  
  // Admin routes
  '/home/<USER>/users',
  '/home/<USER>/operations',
  '/home/<USER>/roles',
  '/home/<USER>/config',
  '/home/<USER>/audittrail',
  
  // Reports routes (lazy loaded)
  '/home/<USER>/production',
  '/home/<USER>/sales',
  '/home/<USER>/stock',
  '/home/<USER>/purchase'
];

/**
 * Test function to check breadcrumb coverage
 */
export function testBreadcrumbCoverage(): { 
  totalRoutes: number; 
  coveredRoutes: number; 
  uncoveredRoutes: string[]; 
  coveragePercentage: number;
} {
  const routeConfigService = new RouteConfigService();
  const uncoveredRoutes: string[] = [];
  let coveredCount = 0;
  
  console.log('🔍 Testing Breadcrumb Coverage for Home HTML Routes...\n');
  
  HOME_HTML_ROUTES.forEach(route => {
    try {
      const breadcrumbs = routeConfigService.getBreadcrumbsForRoute(route);
      if (breadcrumbs && breadcrumbs.length > 0) {
        coveredCount++;
        console.log(`✅ ${route} → [${breadcrumbs.map(b => b.label).join(' > ')}]`);
      } else {
        uncoveredRoutes.push(route);
        console.log(`❌ ${route} → No breadcrumbs found`);
      }
    } catch (error) {
      uncoveredRoutes.push(route);
      console.log(`❌ ${route} → Error: ${error}`);
    }
  });
  
  const coveragePercentage = Math.round((coveredCount / HOME_HTML_ROUTES.length) * 100);
  
  console.log('\n📊 Coverage Summary:');
  console.log(`Total Routes: ${HOME_HTML_ROUTES.length}`);
  console.log(`Covered Routes: ${coveredCount}`);
  console.log(`Uncovered Routes: ${uncoveredRoutes.length}`);
  console.log(`Coverage Percentage: ${coveragePercentage}%`);
  
  if (uncoveredRoutes.length > 0) {
    console.log('\n❌ Uncovered Routes:');
    uncoveredRoutes.forEach(route => console.log(`  - ${route}`));
  }
  
  return {
    totalRoutes: HOME_HTML_ROUTES.length,
    coveredRoutes: coveredCount,
    uncoveredRoutes,
    coveragePercentage
  };
}

// Export the routes for external use
export { HOME_HTML_ROUTES };
