-- =============================================
-- Test Script for ProductId Filtering in OutPass
-- Purpose: Verify that ProductId filtering works correctly for both manual and barcode items
-- Author: PMS System
-- Date: 2025-01-10
-- =============================================

PRINT '========================================';
PRINT 'ProductId Filtering Test Script';
PRINT '========================================';
PRINT '';

-- Test 1: Check OutPass records with manual items for a specific ProductId
PRINT '1. OUTPASS RECORDS WITH MANUAL ITEMS FOR SPECIFIC PRODUCTID';
PRINT '-----------------------------------------------------------';

DECLARE @TestProductId BIGINT = 1; -- Change this to test with different ProductIds

-- Show OutPasses that contain manual items with the specified ProductId
SELECT DISTINCT
    om.OutpassId,
    om.OutpassNumber,
    om.OutpassTo,
    om.OutpassDate,
    om.CreateMode,
    'Manual Item' as ItemType,
    COUNT(oit.OutpassItemId) as ItemCount
FROM OutpassMaster om
INNER JOIN OutpassItemTable oit ON om.OutpassId = oit.OutpassId
INNER JOIN StockProductTable spt ON oit.StockProductId = spt.StockProductId
WHERE spt.ProductId = @TestProductId
  AND oit.StockLabelId IS NULL -- Manual items
GROUP BY om.OutpassId, om.OutpassNumber, om.OutpassTo, om.OutpassDate, om.CreateMode
ORDER BY om.OutpassDate DESC;

PRINT '';

-- Test 2: Check OutPass records with barcode items for a specific ProductId
PRINT '2. OUTPASS RECORDS WITH BARCODE ITEMS FOR SPECIFIC PRODUCTID';
PRINT '------------------------------------------------------------';

-- Show OutPasses that contain barcode items with the specified ProductId
SELECT DISTINCT
    om.OutpassId,
    om.OutpassNumber,
    om.OutpassTo,
    om.OutpassDate,
    om.CreateMode,
    'Barcode Item' as ItemType,
    COUNT(oit.OutpassItemId) as ItemCount
FROM OutpassMaster om
INNER JOIN OutpassItemTable oit ON om.OutpassId = oit.OutpassId
INNER JOIN StockLabelTable slt ON oit.StockLabelId = slt.StockLabelId
WHERE slt.ProductId = @TestProductId
  AND oit.StockLabelId IS NOT NULL -- Barcode items
GROUP BY om.OutpassId, om.OutpassNumber, om.OutpassTo, om.OutpassDate, om.CreateMode
ORDER BY om.OutpassDate DESC;

PRINT '';

-- Test 3: Combined query (what the API should return)
PRINT '3. COMBINED RESULTS (WHAT API SHOULD RETURN)';
PRINT '--------------------------------------------';

-- This query simulates what the API filtering logic should return
SELECT DISTINCT
    om.OutpassId,
    om.OutpassNumber,
    om.OutpassTo,
    om.OutpassDate,
    om.CreateMode,
    om.Status
FROM OutpassMaster om
WHERE 
    -- Check manual items
    EXISTS (
        SELECT 1 
        FROM OutpassItemTable oit
        INNER JOIN StockProductTable spt ON oit.StockProductId = spt.StockProductId
        WHERE oit.OutpassId = om.OutpassId
          AND oit.StockProductId IS NOT NULL
          AND spt.ProductId = @TestProductId
    )
    OR
    -- Check barcode items
    EXISTS (
        SELECT 1 
        FROM OutpassItemTable oit
        INNER JOIN StockLabelTable slt ON oit.StockLabelId = slt.StockLabelId
        WHERE oit.OutpassId = om.OutpassId
          AND oit.StockLabelId IS NOT NULL
          AND slt.ProductId = @TestProductId
    )
ORDER BY om.OutpassDate DESC;

PRINT '';

-- Test 4: Detailed breakdown by ProductId
PRINT '4. DETAILED BREAKDOWN BY PRODUCTID';
PRINT '----------------------------------';

-- Show which ProductIds are available in OutPass items
SELECT 
    p.ProductId,
    p.ProductName,
    COUNT(DISTINCT CASE WHEN oit.StockLabelId IS NULL THEN om.OutpassId END) as OutPassesWithManualItems,
    COUNT(DISTINCT CASE WHEN oit.StockLabelId IS NOT NULL THEN om.OutpassId END) as OutPassesWithBarcodeItems,
    COUNT(DISTINCT om.OutpassId) as TotalOutPasses
FROM ProductMaster p
LEFT JOIN StockProductTable spt ON p.ProductId = spt.ProductId
LEFT JOIN OutpassItemTable oit ON spt.StockProductId = oit.StockProductId
LEFT JOIN OutpassMaster om ON oit.OutpassId = om.OutpassId
WHERE om.OutpassId IS NOT NULL
GROUP BY p.ProductId, p.ProductName

UNION

SELECT 
    p.ProductId,
    p.ProductName,
    0 as OutPassesWithManualItems,
    COUNT(DISTINCT om.OutpassId) as OutPassesWithBarcodeItems,
    COUNT(DISTINCT om.OutpassId) as TotalOutPasses
FROM ProductMaster p
LEFT JOIN StockLabelTable slt ON p.ProductId = slt.ProductId
LEFT JOIN OutpassItemTable oit ON slt.StockLabelId = oit.StockLabelId
LEFT JOIN OutpassMaster om ON oit.OutpassId = om.OutpassId
WHERE om.OutpassId IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM StockProductTable spt2 
      INNER JOIN OutpassItemTable oit2 ON spt2.StockProductId = oit2.StockProductId
      WHERE spt2.ProductId = p.ProductId
  )
GROUP BY p.ProductId, p.ProductName
ORDER BY TotalOutPasses DESC, ProductName;

PRINT '';

-- Test 5: Validation queries
PRINT '5. VALIDATION QUERIES';
PRINT '---------------------';

-- Count total OutPasses
SELECT 'Total OutPasses' as Metric, COUNT(*) as Count FROM OutpassMaster;

-- Count OutPasses with manual items
SELECT 'OutPasses with Manual Items' as Metric, COUNT(DISTINCT OutpassId) as Count 
FROM OutpassItemTable WHERE StockProductId IS NOT NULL AND StockLabelId IS NULL;

-- Count OutPasses with barcode items
SELECT 'OutPasses with Barcode Items' as Metric, COUNT(DISTINCT OutpassId) as Count 
FROM OutpassItemTable WHERE StockLabelId IS NOT NULL;

-- Count OutPasses with mixed items
SELECT 'OutPasses with Mixed Items' as Metric, COUNT(*) as Count
FROM (
    SELECT OutpassId
    FROM OutpassItemTable
    GROUP BY OutpassId
    HAVING COUNT(CASE WHEN StockLabelId IS NULL THEN 1 END) > 0
       AND COUNT(CASE WHEN StockLabelId IS NOT NULL THEN 1 END) > 0
) mixed;

PRINT '';
PRINT '========================================';
PRINT 'Test Complete';
PRINT '';
PRINT 'INSTRUCTIONS FOR TESTING:';
PRINT '1. Change @TestProductId variable to test different products';
PRINT '2. Compare API results with "Combined Results" query';
PRINT '3. Verify that filtering returns correct OutPass records';
PRINT '4. Test both manual and barcode item scenarios';
PRINT '========================================';
