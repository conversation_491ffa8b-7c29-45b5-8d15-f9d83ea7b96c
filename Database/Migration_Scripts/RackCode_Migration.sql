-- =============================================
-- RackMaster RackCode Migration Script
-- Purpose: Migrate existing RackMaster records to use standardized RackCode format
-- Format: {StorePrefix}R-{SequenceNumber} (e.g., "RMSR-001", "FGWR-002")
-- Author: PMS System
-- Date: 2025-01-10
-- =============================================

-- Create helper function for generating store prefix
IF OBJECT_ID('dbo.fn_GenerateStorePrefix', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GenerateStorePrefix;
GO

CREATE FUNCTION dbo.fn_GenerateStorePrefix(@StoreName VARCHAR(500))
RETURNS VARCHAR(10)
AS
BEGIN
    DECLARE @Prefix VARCHAR(10) = '';
    DECLARE @Word VARCHAR(100);
    DECLARE @FirstChar CHAR(1);

    -- Handle null or empty input
    IF @StoreName IS NULL OR LTRIM(RTRIM(@StoreName)) = ''
        RETURN '';

    -- Split store name into words and process each word
    DECLARE @Pos INT = 1;
    DECLARE @NextPos INT;
    DECLARE @CleanName VARCHAR(500) = LTRIM(RTRIM(@StoreName));

    WHILE @Pos <= LEN(@CleanName)
    BEGIN
        -- Find next space or end of string
        SET @NextPos = CHARINDEX(' ', @CleanName, @Pos);
        IF @NextPos = 0
            SET @NextPos = LEN(@CleanName) + 1;

        -- Extract word
        SET @Word = LTRIM(RTRIM(SUBSTRING(@CleanName, @Pos, @NextPos - @Pos)));

        IF LEN(@Word) > 0
        BEGIN
            -- Find first alphabetic character in the word
            DECLARE @CharPos INT = 1;
            WHILE @CharPos <= LEN(@Word)
            BEGIN
                SET @FirstChar = UPPER(SUBSTRING(@Word, @CharPos, 1));
                -- Check if character is alphabetic using T-SQL compatible method
                IF @FirstChar >= 'A' AND @FirstChar <= 'Z'
                BEGIN
                    SET @Prefix = @Prefix + @FirstChar;
                    BREAK;
                END
                SET @CharPos = @CharPos + 1;
            END
        END

        -- Move to next word
        SET @Pos = @NextPos + 1;

        -- Skip multiple spaces
        WHILE @Pos <= LEN(@CleanName) AND SUBSTRING(@CleanName, @Pos, 1) = ' '
        BEGIN
            SET @Pos = @Pos + 1;
        END
    END

    RETURN @Prefix;
END
GO

-- Enable transaction mode for safety
BEGIN TRANSACTION RackCodeMigration;

-- Create backup table for rollback purposes
IF NOT EXISTS (SELECT *
FROM sys.objects
WHERE object_id = OBJECT_ID(N'[dbo].[RackMaster_Backup_RackCodeMigration]') AND type in (N'U'))
BEGIN
    SELECT *
    INTO RackMaster_Backup_RackCodeMigration
    FROM RackMaster;
    PRINT 'Backup table created: RackMaster_Backup_RackCodeMigration';
END

-- Create temporary table to store generated prefixes and avoid conflicts
CREATE TABLE #StorePrefixes
(
    StoreId BIGINT,
    StoreName VARCHAR(500),
    BasePrefix VARCHAR(10),
    FinalPrefix VARCHAR(20),
    ConflictResolved BIT DEFAULT 0
);

-- Create temporary table for rack code generation
CREATE TABLE #RackCodeGeneration
(
    RackId BIGINT,
    StoreId BIGINT,
    StoreName VARCHAR(500),
    CurrentRackCode VARCHAR(50),
    NewRackCode VARCHAR(50),
    SequenceNumber INT,
    NeedsUpdate BIT DEFAULT 0
);

-- =============================================
-- STEP 1: Generate base prefixes for all stores
-- =============================================
INSERT INTO #StorePrefixes
    (StoreId, StoreName, BasePrefix)
SELECT DISTINCT
    s.StoreId,
    s.StoreName,
    dbo.fn_GenerateStorePrefix(s.StoreName) as BasePrefix
FROM StoreMaster s
WHERE s.StoreId IN (
    SELECT DISTINCT StoreId
FROM RackMaster
WHERE StoreId IS NOT NULL
);

-- =============================================
-- STEP 2: Resolve prefix conflicts across stores
-- =============================================
DECLARE @StoreId BIGINT, @StoreName VARCHAR(500), @BasePrefix VARCHAR(10), @FinalPrefix VARCHAR(20);
DECLARE @ConflictCount INT, @AdditionalChar CHAR(1), @SuffixNum INT;

DECLARE store_cursor CURSOR FOR
SELECT StoreId, StoreName, BasePrefix
FROM #StorePrefixes
ORDER BY StoreId;

OPEN store_cursor;
FETCH NEXT FROM store_cursor INTO @StoreId, @StoreName, @BasePrefix;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @FinalPrefix = @BasePrefix;

    -- Check if this prefix conflicts with other stores
    SELECT @ConflictCount = COUNT(*)
    FROM #StorePrefixes
    WHERE BasePrefix = @BasePrefix AND StoreId != @StoreId AND ConflictResolved = 1;

    IF @ConflictCount > 0
    BEGIN
        -- Try to add additional characters from store name
        DECLARE @UsedChars VARCHAR(50) = @BasePrefix;
        DECLARE @CharIndex INT = 1;
        DECLARE @Found BIT = 0;

        WHILE @CharIndex <= LEN(@StoreName) AND @Found = 0
        BEGIN
            SET @AdditionalChar = UPPER(SUBSTRING(@StoreName, @CharIndex, 1));

            -- Check if character is alphabetic using T-SQL compatible method
            IF @AdditionalChar >= 'A' AND @AdditionalChar <= 'Z' AND CHARINDEX(@AdditionalChar, @UsedChars) = 0
            BEGIN
                SET @FinalPrefix = @BasePrefix + @AdditionalChar;

                -- Check if this new prefix is unique
                IF NOT EXISTS (SELECT 1
                FROM #StorePrefixes
                WHERE FinalPrefix = @FinalPrefix AND StoreId != @StoreId)
                BEGIN
                    SET @Found = 1;
                END
            END

            SET @CharIndex = @CharIndex + 1;
        END

        -- If still conflicting, add numeric suffix
        IF @Found = 0
        BEGIN
            SET @SuffixNum = 1;
            WHILE EXISTS (SELECT 1
            FROM #StorePrefixes
            WHERE FinalPrefix = @BasePrefix + CAST(@SuffixNum AS VARCHAR(2)) AND StoreId != @StoreId)
            BEGIN
                SET @SuffixNum = @SuffixNum + 1;
            END
            SET @FinalPrefix = @BasePrefix + CAST(@SuffixNum AS VARCHAR(2));
        END
    END

    -- Update the final prefix
    UPDATE #StorePrefixes 
    SET FinalPrefix = @FinalPrefix, ConflictResolved = 1
    WHERE StoreId = @StoreId;

    FETCH NEXT FROM store_cursor INTO @StoreId, @StoreName, @BasePrefix;
END

CLOSE store_cursor;
DEALLOCATE store_cursor;

-- =============================================
-- STEP 3: Generate new rack codes for records that need updating
-- =============================================
INSERT INTO #RackCodeGeneration
    (RackId, StoreId, StoreName, CurrentRackCode, NeedsUpdate)
SELECT
    r.RackId,
    r.StoreId,
    s.StoreName,
    r.RackCode,
    CASE
        WHEN r.RackCode IS NULL OR
        r.RackCode = '' OR
        r.RackCode LIKE '%temp%' OR
        r.RackCode LIKE '%placeholder%' OR
        LEN(r.RackCode) < 3 OR
        r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]'  -- Add this line to update non-standard patterns
        THEN 1
        ELSE 0
    END as NeedsUpdate
FROM RackMaster r
    INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
WHERE r.StoreId IS NOT NULL;

-- Generate sequence numbers for records that need updating
DECLARE @CurrentStoreId BIGINT = -1;
DECLARE @SequenceNum INT = 1;
DECLARE @CurrentPrefix VARCHAR(20);
DECLARE @RackId BIGINT;

DECLARE rack_cursor CURSOR FOR
SELECT rg.RackId, rg.StoreId, sp.FinalPrefix
FROM #RackCodeGeneration rg
    INNER JOIN #StorePrefixes sp ON rg.StoreId = sp.StoreId
WHERE rg.NeedsUpdate = 1
ORDER BY rg.StoreId, rg.RackId;

OPEN rack_cursor;
FETCH NEXT FROM rack_cursor INTO @RackId, @StoreId, @CurrentPrefix;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- Reset sequence for new store
    IF @CurrentStoreId != @StoreId
    BEGIN
        SET @CurrentStoreId = @StoreId;
        SET @SequenceNum = 1;

        -- Find the highest existing sequence number for this store/prefix
        SELECT @SequenceNum = ISNULL(MAX(
            CASE
                WHEN r.RackCode LIKE @CurrentPrefix + 'R-%'
                THEN TRY_CAST(RIGHT(r.RackCode, 3) AS INT) + 1
                ELSE 1
            END
        ), 1)
        FROM RackMaster r
        WHERE r.StoreId = @CurrentStoreId;

        -- Also check what's already been generated in this session
        DECLARE @MaxGenerated INT;
        SELECT @MaxGenerated = ISNULL(MAX(SequenceNumber), 0)
        FROM #RackCodeGeneration
        WHERE StoreId = @CurrentStoreId AND NewRackCode IS NOT NULL;

        IF @MaxGenerated >= @SequenceNum
            SET @SequenceNum = @MaxGenerated + 1;
    END

    -- Generate new rack code
    DECLARE @NewRackCode VARCHAR(50) = @CurrentPrefix + 'R-' + RIGHT('000' + CAST(@SequenceNum AS VARCHAR(3)), 3);

    -- Ensure uniqueness within store (check both existing and newly generated codes)
    WHILE EXISTS (SELECT 1
        FROM RackMaster
        WHERE StoreId = @CurrentStoreId AND RackCode = @NewRackCode)
        OR EXISTS (SELECT 1
        FROM #RackCodeGeneration
        WHERE StoreId = @CurrentStoreId AND NewRackCode = @NewRackCode AND RackId != @RackId)
    BEGIN
        SET @SequenceNum = @SequenceNum + 1;
        SET @NewRackCode = @CurrentPrefix + 'R-' + RIGHT('000' + CAST(@SequenceNum AS VARCHAR(3)), 3);
    END

    -- Update the generation table
    UPDATE #RackCodeGeneration
    SET NewRackCode = @NewRackCode, SequenceNumber = @SequenceNum
    WHERE RackId = @RackId;

    SET @SequenceNum = @SequenceNum + 1;

    FETCH NEXT FROM rack_cursor INTO @RackId, @StoreId, @CurrentPrefix;
END

CLOSE rack_cursor;
DEALLOCATE rack_cursor;

-- =============================================
-- STEP 4: Validation before update
-- =============================================
DECLARE @ValidationErrors INT = 0;

-- Check for duplicate new rack codes
SELECT @ValidationErrors = COUNT(*)
FROM (
    SELECT NewRackCode, StoreId, COUNT(*) as DuplicateCount
    FROM #RackCodeGeneration
    WHERE NeedsUpdate = 1 AND NewRackCode IS NOT NULL
    GROUP BY NewRackCode, StoreId
    HAVING COUNT(*) > 1
) duplicates;

IF @ValidationErrors > 0
BEGIN
    PRINT 'ERROR: Duplicate rack codes would be generated. Rolling back transaction.';
    ROLLBACK TRANSACTION RackCodeMigration;
    RETURN;
END

-- Check for conflicts with existing codes
SELECT @ValidationErrors = COUNT(*)
FROM #RackCodeGeneration rg
    INNER JOIN RackMaster r ON rg.StoreId = r.StoreId AND rg.NewRackCode = r.RackCode
WHERE rg.NeedsUpdate = 1 AND rg.RackId != r.RackId;

IF @ValidationErrors > 0
BEGIN
    PRINT 'ERROR: New rack codes conflict with existing codes. Rolling back transaction.';
    ROLLBACK TRANSACTION RackCodeMigration;
    RETURN;
END

-- =============================================
-- STEP 5: Perform the actual updates
-- =============================================
UPDATE r
SET RackCode = rg.NewRackCode
FROM RackMaster r
    INNER JOIN #RackCodeGeneration rg ON r.RackId = rg.RackId
WHERE rg.NeedsUpdate = 1 AND rg.NewRackCode IS NOT NULL;

-- =============================================
-- STEP 6: Report results
-- =============================================
DECLARE @UpdatedCount INT;
SELECT @UpdatedCount = COUNT(*)
FROM #RackCodeGeneration
WHERE NeedsUpdate = 1 AND NewRackCode IS NOT NULL;

PRINT 'Migration completed successfully!';
PRINT 'Records updated: ' + CAST(@UpdatedCount AS VARCHAR(10));

-- Show summary of changes
SELECT
    s.StoreName,
    sp.BasePrefix,
    sp.FinalPrefix,
    COUNT(rg.RackId) as RecordsUpdated
FROM #StorePrefixes sp
    INNER JOIN StoreMaster s ON sp.StoreId = s.StoreId
    LEFT JOIN #RackCodeGeneration rg ON sp.StoreId = rg.StoreId AND rg.NeedsUpdate = 1
GROUP BY s.StoreName, sp.BasePrefix, sp.FinalPrefix
ORDER BY s.StoreName;

-- Commit the transaction
COMMIT TRANSACTION RackCodeMigration;

-- Clean up temporary tables
DROP TABLE #StorePrefixes;
DROP TABLE #RackCodeGeneration;

PRINT 'Migration completed successfully. Backup table: RackMaster_Backup_RackCodeMigration';
