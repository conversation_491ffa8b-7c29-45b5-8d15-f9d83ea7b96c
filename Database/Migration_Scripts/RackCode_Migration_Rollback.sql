-- =============================================
-- RackMaster RackCode Migration Rollback Script
-- Purpose: Rollback the RackCode migration if issues are encountered
-- Author: PMS System
-- Date: 2025-01-10
-- =============================================

-- Check if backup table exists
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RackMaster_Backup_RackCodeMigration]') AND type in (N'U'))
BEGIN
    PRINT 'ERROR: Backup table RackMaster_Backup_RackCodeMigration does not exist.';
    PRINT 'Cannot perform rollback without backup table.';
    RETURN;
END

-- Begin rollback transaction
BEGIN TRANSACTION RackCodeMigrationRollback;

-- Show current state before rollback
PRINT 'Current RackMaster record count: ' + CAST((SELECT COUNT(*) FROM RackMaster) AS VARCHAR(10));
PRINT 'Backup table record count: ' + CAST((SELECT COUNT(*) FROM RackMaster_Backup_RackCodeMigration) AS VARCHAR(10));

-- Verify backup table integrity
DECLARE @BackupCount INT, @OriginalCount INT;
SELECT @BackupCount = COUNT(*) FROM RackMaster_Backup_RackCodeMigration;
SELECT @OriginalCount = COUNT(*) FROM RackMaster;

IF @BackupCount = 0
BEGIN
    PRINT 'ERROR: Backup table is empty. Rollback aborted.';
    ROLLBACK TRANSACTION RackCodeMigrationRollback;
    RETURN;
END

-- Create a temporary table to track changes
CREATE TABLE #RollbackChanges (
    RackId BIGINT,
    CurrentRackCode VARCHAR(50),
    BackupRackCode VARCHAR(50),
    ChangeType VARCHAR(20)
);

-- Identify records that were changed
INSERT INTO #RollbackChanges (RackId, CurrentRackCode, BackupRackCode, ChangeType)
SELECT 
    r.RackId,
    r.RackCode as CurrentRackCode,
    b.RackCode as BackupRackCode,
    CASE 
        WHEN r.RackCode != b.RackCode OR (r.RackCode IS NULL AND b.RackCode IS NOT NULL) OR (r.RackCode IS NOT NULL AND b.RackCode IS NULL)
        THEN 'MODIFIED'
        ELSE 'UNCHANGED'
    END as ChangeType
FROM RackMaster r
INNER JOIN RackMaster_Backup_RackCodeMigration b ON r.RackId = b.RackId;

-- Show summary of changes to be rolled back
DECLARE @ModifiedCount INT;
SELECT @ModifiedCount = COUNT(*) FROM #RollbackChanges WHERE ChangeType = 'MODIFIED';

PRINT 'Records to be rolled back: ' + CAST(@ModifiedCount AS VARCHAR(10));

IF @ModifiedCount > 0
BEGIN
    PRINT 'Sample of changes to be rolled back:';
    SELECT TOP 10 
        RackId,
        CurrentRackCode,
        BackupRackCode
    FROM #RollbackChanges 
    WHERE ChangeType = 'MODIFIED';
END

-- Perform the rollback
UPDATE r
SET 
    RackCode = b.RackCode,
    RackName = b.RackName,
    RackDesc = b.RackDesc,
    RackAddedBy = b.RackAddedBy,
    RackAddedDate = b.RackAddedDate,
    Disabled = b.Disabled,
    DisabledBy = b.DisabledBy,
    DisabledDate = b.DisabledDate
FROM RackMaster r
INNER JOIN RackMaster_Backup_RackCodeMigration b ON r.RackId = b.RackId;

-- Handle any records that might have been added after backup (if any)
DECLARE @NewRecordsCount INT;
SELECT @NewRecordsCount = COUNT(*)
FROM RackMaster r
LEFT JOIN RackMaster_Backup_RackCodeMigration b ON r.RackId = b.RackId
WHERE b.RackId IS NULL;

IF @NewRecordsCount > 0
BEGIN
    PRINT 'WARNING: ' + CAST(@NewRecordsCount AS VARCHAR(10)) + ' records exist in RackMaster that are not in the backup.';
    PRINT 'These records will not be affected by the rollback.';
    
    SELECT 
        RackId,
        StoreId,
        RackName,
        RackCode,
        RackAddedDate
    FROM RackMaster r
    LEFT JOIN RackMaster_Backup_RackCodeMigration b ON r.RackId = b.RackId
    WHERE b.RackId IS NULL;
END

-- Handle any records that might have been deleted after backup (restore them)
INSERT INTO RackMaster (StoreId, RackName, RackCode, RackDesc, RackAddedBy, RackAddedDate, Disabled, DisabledBy, DisabledDate)
SELECT 
    b.StoreId,
    b.RackName,
    b.RackCode,
    b.RackDesc,
    b.RackAddedBy,
    b.RackAddedDate,
    b.Disabled,
    b.DisabledBy,
    b.DisabledDate
FROM RackMaster_Backup_RackCodeMigration b
LEFT JOIN RackMaster r ON b.RackId = r.RackId
WHERE r.RackId IS NULL;

DECLARE @RestoredCount INT = @@ROWCOUNT;

IF @RestoredCount > 0
BEGIN
    PRINT 'Restored ' + CAST(@RestoredCount AS VARCHAR(10)) + ' deleted records from backup.';
END

-- Verify rollback success
DECLARE @FinalCount INT;
SELECT @FinalCount = COUNT(*) FROM RackMaster;

PRINT 'Rollback completed.';
PRINT 'Final RackMaster record count: ' + CAST(@FinalCount AS VARCHAR(10));

-- Show final verification
SELECT 
    'ROLLBACK_SUMMARY' as Operation,
    @ModifiedCount as RecordsRolledBack,
    @RestoredCount as RecordsRestored,
    @NewRecordsCount as RecordsNotInBackup,
    @FinalCount as FinalRecordCount;

-- Commit the rollback transaction
COMMIT TRANSACTION RackCodeMigrationRollback;

-- Clean up
DROP TABLE #RollbackChanges;

PRINT 'Rollback transaction completed successfully.';
PRINT 'Note: Backup table RackMaster_Backup_RackCodeMigration has been preserved for future reference.';
PRINT 'You can drop it manually when you are confident the rollback was successful:';
PRINT 'DROP TABLE RackMaster_Backup_RackCodeMigration;';

-- Drop the helper function if it exists
IF OBJECT_ID('dbo.fn_GenerateStorePrefix', 'FN') IS NOT NULL
BEGIN
    DROP FUNCTION dbo.fn_GenerateStorePrefix;
    PRINT 'Helper function dbo.fn_GenerateStorePrefix has been dropped.';
END
