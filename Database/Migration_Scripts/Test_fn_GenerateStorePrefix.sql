-- =============================================
-- Test Script for fn_GenerateStorePrefix Function
-- Purpose: Verify the function works correctly with various store names
-- Author: PMS System
-- Date: 2025-01-10
-- =============================================

-- Create the function first (if not already created)
IF OBJECT_ID('dbo.fn_GenerateStorePrefix', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GenerateStorePrefix;
GO

CREATE FUNCTION dbo.fn_GenerateStorePrefix(@StoreName VARCHAR(500))
RETURNS VARCHAR(10)
AS
BEGIN
    DECLARE @Prefix VARCHAR(10) = '';
    DECLARE @Word VARCHAR(100);
    DECLARE @FirstChar CHAR(1);
    
    -- Handle null or empty input
    IF @StoreName IS NULL OR LTRIM(RTRIM(@StoreName)) = ''
        RETURN '';
    
    -- Split store name into words and process each word
    DECLARE @Pos INT = 1;
    DECLARE @NextPos INT;
    DECLARE @CleanName VARCHAR(500) = LTRIM(RTRIM(@StoreName));
    
    WHILE @Pos <= LEN(@CleanName)
    BEGIN
        -- Find next space or end of string
        SET @NextPos = CHARINDEX(' ', @CleanName, @Pos);
        IF @NextPos = 0
            SET @NextPos = LEN(@CleanName) + 1;
            
        -- Extract word
        SET @Word = LTRIM(RTRIM(SUBSTRING(@CleanName, @Pos, @NextPos - @Pos)));
        
        IF LEN(@Word) > 0
        BEGIN
            -- Find first alphabetic character in the word
            DECLARE @CharPos INT = 1;
            WHILE @CharPos <= LEN(@Word)
            BEGIN
                SET @FirstChar = UPPER(SUBSTRING(@Word, @CharPos, 1));
                -- Check if character is alphabetic using T-SQL compatible method
                IF @FirstChar >= 'A' AND @FirstChar <= 'Z'
                BEGIN
                    SET @Prefix = @Prefix + @FirstChar;
                    BREAK;
                END
                SET @CharPos = @CharPos + 1;
            END
        END
        
        -- Move to next word
        SET @Pos = @NextPos + 1;
        
        -- Skip multiple spaces
        WHILE @Pos <= LEN(@CleanName) AND SUBSTRING(@CleanName, @Pos, 1) = ' '
        BEGIN
            SET @Pos = @Pos + 1;
        END
    END
    
    RETURN @Prefix;
END
GO

-- Test the function with various store names
PRINT '========================================';
PRINT 'Testing fn_GenerateStorePrefix Function';
PRINT '========================================';
PRINT '';

-- Test cases
SELECT 
    'Test Case' as TestType,
    'Store Name' as StoreName,
    'Expected' as Expected,
    'Actual' as Actual,
    'Result' as TestResult
UNION ALL
SELECT 
    '1. Normal Case',
    'Raw Material Store',
    'RMS',
    dbo.fn_GenerateStorePrefix('Raw Material Store'),
    CASE WHEN dbo.fn_GenerateStorePrefix('Raw Material Store') = 'RMS' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '2. Multiple Words',
    'Finished Goods Warehouse',
    'FGW',
    dbo.fn_GenerateStorePrefix('Finished Goods Warehouse'),
    CASE WHEN dbo.fn_GenerateStorePrefix('Finished Goods Warehouse') = 'FGW' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '3. Single Word',
    'Warehouse',
    'W',
    dbo.fn_GenerateStorePrefix('Warehouse'),
    CASE WHEN dbo.fn_GenerateStorePrefix('Warehouse') = 'W' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '4. Extra Spaces',
    '  Raw   Material   Store  ',
    'RMS',
    dbo.fn_GenerateStorePrefix('  Raw   Material   Store  '),
    CASE WHEN dbo.fn_GenerateStorePrefix('  Raw   Material   Store  ') = 'RMS' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '5. Numbers in Name',
    'Store 123 Main',
    'SM',
    dbo.fn_GenerateStorePrefix('Store 123 Main'),
    CASE WHEN dbo.fn_GenerateStorePrefix('Store 123 Main') = 'SM' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '6. Special Characters',
    'Raw-Material & Store',
    'RMS',
    dbo.fn_GenerateStorePrefix('Raw-Material & Store'),
    CASE WHEN dbo.fn_GenerateStorePrefix('Raw-Material & Store') = 'RMS' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '7. Mixed Case',
    'raw material STORE',
    'RMS',
    dbo.fn_GenerateStorePrefix('raw material STORE'),
    CASE WHEN dbo.fn_GenerateStorePrefix('raw material STORE') = 'RMS' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '8. Empty String',
    '',
    '',
    dbo.fn_GenerateStorePrefix(''),
    CASE WHEN dbo.fn_GenerateStorePrefix('') = '' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '9. NULL Input',
    'NULL',
    '',
    ISNULL(dbo.fn_GenerateStorePrefix(NULL), ''),
    CASE WHEN ISNULL(dbo.fn_GenerateStorePrefix(NULL), '') = '' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '10. Only Spaces',
    '   ',
    '',
    dbo.fn_GenerateStorePrefix('   '),
    CASE WHEN dbo.fn_GenerateStorePrefix('   ') = '' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '11. Numbers Only',
    '123 456',
    '',
    dbo.fn_GenerateStorePrefix('123 456'),
    CASE WHEN dbo.fn_GenerateStorePrefix('123 456') = '' THEN 'PASS' ELSE 'FAIL' END
UNION ALL
SELECT 
    '12. Word Starting with Number',
    '1st Floor Store',
    'FS',
    dbo.fn_GenerateStorePrefix('1st Floor Store'),
    CASE WHEN dbo.fn_GenerateStorePrefix('1st Floor Store') = 'FS' THEN 'PASS' ELSE 'FAIL' END
ORDER BY TestType;

PRINT '';
PRINT 'Additional Test Cases:';

-- Test conflict scenarios
SELECT 
    'Conflict Test' as TestType,
    StoreName,
    dbo.fn_GenerateStorePrefix(StoreName) as GeneratedPrefix
FROM (
    VALUES 
        ('Raw Material Store'),
        ('Retail Marketing Store'),
        ('Regional Management Store'),
        ('Finished Goods Warehouse'),
        ('Final Goods Warehouse'),
        ('Factory Gate Warehouse')
) AS TestStores(StoreName);

PRINT '';
PRINT 'Summary:';

-- Count pass/fail results
WITH TestResults AS (
    SELECT 
        CASE WHEN dbo.fn_GenerateStorePrefix('Raw Material Store') = 'RMS' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('Finished Goods Warehouse') = 'FGW' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('Warehouse') = 'W' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('  Raw   Material   Store  ') = 'RMS' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('Store 123 Main') = 'SM' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('Raw-Material & Store') = 'RMS' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('raw material STORE') = 'RMS' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('') = '' THEN 1 ELSE 0 END +
        CASE WHEN ISNULL(dbo.fn_GenerateStorePrefix(NULL), '') = '' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('   ') = '' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('123 456') = '' THEN 1 ELSE 0 END +
        CASE WHEN dbo.fn_GenerateStorePrefix('1st Floor Store') = 'FS' THEN 1 ELSE 0 END
        as PassedTests,
        12 as TotalTests
)
SELECT 
    PassedTests,
    TotalTests,
    TotalTests - PassedTests as FailedTests,
    CAST(PassedTests * 100.0 / TotalTests AS DECIMAL(5,2)) as PassPercentage
FROM TestResults;

PRINT '';
PRINT 'Test completed. Function is ready for use in migration script.';

-- Clean up (comment out if you want to keep the function for migration)
-- DROP FUNCTION dbo.fn_GenerateStorePrefix;
