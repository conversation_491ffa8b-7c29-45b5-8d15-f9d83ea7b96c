-- =============================================
-- RackMaster RackCode Migration Script (Simplified Version)
-- Purpose: Migrate existing RackMaster records to use standardized RackCode format
-- Format: {StorePrefix}R-{SequenceNumber} (e.g., "RMSR-001", "FGWR-002")
-- Author: PMS System
-- Date: 2025-01-10
-- =============================================

-- Create helper function for generating store prefix
IF OBJECT_ID('dbo.fn_GenerateStorePrefix', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GenerateStorePrefix;
GO

CREATE FUNCTION dbo.fn_GenerateStorePrefix(@StoreName VARCHAR(500))
RETURNS VARCHAR(10)
AS
BEGIN
    DECLARE @Prefix VARCHAR(10) = '';
    DECLARE @Word VARCHAR(100);
    DECLARE @FirstChar CHAR(1);
    
    -- Handle null or empty input
    IF @StoreName IS NULL OR LTRIM(RTRIM(@StoreName)) = ''
        RETURN '';
    
    -- Split store name into words and process each word
    DECLARE @Pos INT = 1;
    DECLARE @NextPos INT;
    DECLARE @CleanName VARCHAR(500) = LTRIM(RTRIM(@StoreName));
    
    WHILE @Pos <= LEN(@CleanName)
    BEGIN
        -- Find next space or end of string
        SET @NextPos = CHARINDEX(' ', @CleanName, @Pos);
        IF @NextPos = 0
            SET @NextPos = LEN(@CleanName) + 1;
            
        -- Extract word
        SET @Word = LTRIM(RTRIM(SUBSTRING(@CleanName, @Pos, @NextPos - @Pos)));
        
        IF LEN(@Word) > 0
        BEGIN
            -- Find first alphabetic character in the word
            DECLARE @CharPos INT = 1;
            WHILE @CharPos <= LEN(@Word)
            BEGIN
                SET @FirstChar = UPPER(SUBSTRING(@Word, @CharPos, 1));
                -- Check if character is alphabetic using T-SQL compatible method
                IF @FirstChar >= 'A' AND @FirstChar <= 'Z'
                BEGIN
                    SET @Prefix = @Prefix + @FirstChar;
                    BREAK;
                END
                SET @CharPos = @CharPos + 1;
            END
        END
        
        -- Move to next word
        SET @Pos = @NextPos + 1;
        
        -- Skip multiple spaces
        WHILE @Pos <= LEN(@CleanName) AND SUBSTRING(@CleanName, @Pos, 1) = ' '
        BEGIN
            SET @Pos = @Pos + 1;
        END
    END
    
    RETURN @Prefix;
END
GO

-- Enable transaction mode for safety
BEGIN TRANSACTION RackCodeMigration;

-- Create backup table for rollback purposes
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RackMaster_Backup_RackCodeMigration]') AND type in (N'U'))
BEGIN
    SELECT * INTO RackMaster_Backup_RackCodeMigration FROM RackMaster;
    PRINT 'Backup table created: RackMaster_Backup_RackCodeMigration';
END

-- Create a working table with all the information we need
CREATE TABLE #RackMigration (
    RackId BIGINT,
    StoreId BIGINT,
    StoreName VARCHAR(500),
    CurrentRackCode VARCHAR(50),
    BasePrefix VARCHAR(10),
    FinalPrefix VARCHAR(20),
    NewRackCode VARCHAR(50),
    SequenceNumber INT,
    NeedsUpdate BIT
);

-- Populate the working table with records that need updating
INSERT INTO #RackMigration (RackId, StoreId, StoreName, CurrentRackCode, BasePrefix, NeedsUpdate)
SELECT 
    r.RackId,
    r.StoreId,
    s.StoreName,
    r.RackCode,
    dbo.fn_GenerateStorePrefix(s.StoreName) as BasePrefix,
    CASE 
        WHEN r.RackCode IS NULL OR
             r.RackCode = '' OR
             r.RackCode LIKE '%temp%' OR
             r.RackCode LIKE '%placeholder%' OR
             LEN(r.RackCode) < 3 OR
             r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]'
        THEN 1 
        ELSE 0 
    END as NeedsUpdate
FROM RackMaster r
INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
WHERE r.StoreId IS NOT NULL;

-- Show how many records will be updated
DECLARE @RecordsToUpdate INT;
SELECT @RecordsToUpdate = COUNT(*) FROM #RackMigration WHERE NeedsUpdate = 1;
PRINT 'Records to be updated: ' + CAST(@RecordsToUpdate AS VARCHAR(10));

IF @RecordsToUpdate = 0
BEGIN
    PRINT 'No records need updating. Rolling back transaction.';
    ROLLBACK TRANSACTION RackCodeMigration;
    DROP TABLE #RackMigration;
    RETURN;
END

-- Resolve prefix conflicts by adding additional characters
UPDATE rm1
SET FinalPrefix = rm1.BasePrefix + 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM #RackMigration rm2 
            WHERE rm2.StoreId != rm1.StoreId 
            AND rm2.BasePrefix = rm1.BasePrefix
        )
        THEN (
            -- Add first unused letter from store name
            SELECT TOP 1 UPPER(SUBSTRING(rm1.StoreName, n.number, 1))
            FROM (
                SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) as number
                FROM sys.objects
            ) n
            WHERE n.number <= LEN(rm1.StoreName)
            AND UPPER(SUBSTRING(rm1.StoreName, n.number, 1)) >= 'A' 
            AND UPPER(SUBSTRING(rm1.StoreName, n.number, 1)) <= 'Z'
            AND CHARINDEX(UPPER(SUBSTRING(rm1.StoreName, n.number, 1)), rm1.BasePrefix) = 0
            AND NOT EXISTS (
                SELECT 1 FROM #RackMigration rm3
                WHERE rm3.StoreId != rm1.StoreId
                AND rm3.BasePrefix + UPPER(SUBSTRING(rm1.StoreName, n.number, 1)) = rm1.BasePrefix + UPPER(SUBSTRING(rm1.StoreName, n.number, 1))
            )
        )
        ELSE ''
    END
FROM #RackMigration rm1;

-- For any remaining conflicts, add numeric suffixes
DECLARE @Counter INT = 1;
WHILE EXISTS (
    SELECT FinalPrefix, COUNT(*) 
    FROM #RackMigration 
    WHERE NeedsUpdate = 1 
    GROUP BY FinalPrefix 
    HAVING COUNT(*) > 1
) AND @Counter <= 10
BEGIN
    UPDATE rm1
    SET FinalPrefix = rm1.FinalPrefix + CAST(@Counter AS VARCHAR(2))
    FROM #RackMigration rm1
    WHERE EXISTS (
        SELECT 1 FROM #RackMigration rm2
        WHERE rm2.StoreId != rm1.StoreId 
        AND rm2.FinalPrefix = rm1.FinalPrefix
        AND rm2.RackId < rm1.RackId  -- Only update the "later" record
    );
    
    SET @Counter = @Counter + 1;
END

-- Generate sequence numbers using ROW_NUMBER()
WITH SequencedRacks AS (
    SELECT 
        RackId,
        StoreId,
        FinalPrefix,
        ROW_NUMBER() OVER (PARTITION BY StoreId, FinalPrefix ORDER BY RackId) as SeqNum
    FROM #RackMigration
    WHERE NeedsUpdate = 1
)
UPDATE rm
SET 
    SequenceNumber = sr.SeqNum,
    NewRackCode = rm.FinalPrefix + 'R-' + RIGHT('000' + CAST(sr.SeqNum AS VARCHAR(3)), 3)
FROM #RackMigration rm
INNER JOIN SequencedRacks sr ON rm.RackId = sr.RackId;

-- Validation: Check for duplicates
DECLARE @DuplicateCount INT;
SELECT @DuplicateCount = COUNT(*)
FROM (
    SELECT NewRackCode, StoreId, COUNT(*) as cnt
    FROM #RackMigration 
    WHERE NeedsUpdate = 1 AND NewRackCode IS NOT NULL
    GROUP BY NewRackCode, StoreId
    HAVING COUNT(*) > 1
) dups;

IF @DuplicateCount > 0
BEGIN
    PRINT 'ERROR: ' + CAST(@DuplicateCount AS VARCHAR(10)) + ' duplicate codes would be generated.';
    
    -- Show the duplicates
    SELECT 'DUPLICATE CODES:' as Issue, NewRackCode, StoreId, COUNT(*) as DuplicateCount
    FROM #RackMigration 
    WHERE NeedsUpdate = 1 AND NewRackCode IS NOT NULL
    GROUP BY NewRackCode, StoreId
    HAVING COUNT(*) > 1;
    
    ROLLBACK TRANSACTION RackCodeMigration;
    DROP TABLE #RackMigration;
    RETURN;
END

-- Validation: Check for conflicts with existing codes
SELECT @DuplicateCount = COUNT(*)
FROM #RackMigration rm
INNER JOIN RackMaster r ON rm.StoreId = r.StoreId AND rm.NewRackCode = r.RackCode
WHERE rm.NeedsUpdate = 1 AND rm.RackId != r.RackId;

IF @DuplicateCount > 0
BEGIN
    PRINT 'ERROR: ' + CAST(@DuplicateCount AS VARCHAR(10)) + ' new codes conflict with existing codes.';
    ROLLBACK TRANSACTION RackCodeMigration;
    DROP TABLE #RackMigration;
    RETURN;
END

-- Perform the actual update
UPDATE r
SET RackCode = rm.NewRackCode
FROM RackMaster r
INNER JOIN #RackMigration rm ON r.RackId = rm.RackId
WHERE rm.NeedsUpdate = 1 AND rm.NewRackCode IS NOT NULL;

-- Report results
DECLARE @UpdatedCount INT = @@ROWCOUNT;
PRINT 'Migration completed successfully!';
PRINT 'Records updated: ' + CAST(@UpdatedCount AS VARCHAR(10));

-- Show summary
SELECT 
    StoreName,
    BasePrefix,
    FinalPrefix,
    COUNT(*) as RecordsUpdated,
    MIN(NewRackCode) as FirstCode,
    MAX(NewRackCode) as LastCode
FROM #RackMigration
WHERE NeedsUpdate = 1
GROUP BY StoreName, BasePrefix, FinalPrefix
ORDER BY StoreName;

-- Commit the transaction
COMMIT TRANSACTION RackCodeMigration;

-- Clean up
DROP TABLE #RackMigration;

PRINT 'Migration completed successfully. Backup table: RackMaster_Backup_RackCodeMigration';
