# RackCode Migration Scripts

This directory contains SQL scripts to migrate existing RackMaster records to use standardized RackCode format.

## Overview

The migration implements the same logic as the `GenerateUniqueRackCode` method in C#:
- **Format**: `{StorePrefix}R-{SequenceNumber}` (e.g., "RMSR-001", "FGWR-002")
- **Store Prefix**: First letter of each word in store name
- **Conflict Resolution**: Adds additional letters or numbers when prefixes conflict
- **Sequence**: 3-digit sequential numbers within each store

## Files

### 1. `RackCode_Migration.sql`
Main migration script that:
- Creates backup table for rollback safety
- Generates unique prefixes for each store
- Resolves prefix conflicts across stores
- Updates RackCode values for records that need migration
- Validates results before committing

### 2. `RackCode_Migration_Rollback.sql`
Rollback script that:
- Restores original RackCode values from backup
- Handles records added/deleted after migration
- Provides detailed rollback summary

### 3. `RackCode_Migration_Validation.sql`
Validation script that:
- Analyzes migration results
- Checks for duplicates and pattern compliance
- Provides store-wise and prefix analysis
- Compares before/after states

### 4. `README_RackCode_Migration.md`
This documentation file

## Usage Instructions

### Pre-Migration Steps

1. **Backup Database** (Recommended)
   ```sql
   -- Create full database backup
   BACKUP DATABASE [YourPMSDatabase] 
   TO DISK = 'C:\Backups\PMS_PreRackCodeMigration.bak'
   ```

2. **Review Current Data**
   ```sql
   -- Check current RackMaster records
   SELECT s.StoreName, r.RackCode, COUNT(*) as RackCount
   FROM RackMaster r
   INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
   GROUP BY s.StoreName, r.RackCode
   ORDER BY s.StoreName;
   ```

### Migration Process

1. **Run Migration Script**
   ```sql
   -- Execute the main migration
   -- This script is safe to run multiple times (idempotent)
   EXEC sp_executesql N'[Content of RackCode_Migration.sql]'
   ```

2. **Validate Results**
   ```sql
   -- Run validation to check migration success
   EXEC sp_executesql N'[Content of RackCode_Migration_Validation.sql]'
   ```

3. **Review Validation Report**
   - Check for any duplicate codes
   - Verify pattern compliance
   - Review store-wise distribution

### If Issues Occur

1. **Run Rollback Script**
   ```sql
   -- Restore original state
   EXEC sp_executesql N'[Content of RackCode_Migration_Rollback.sql]'
   ```

2. **Investigate Issues**
   - Review validation report
   - Check for data inconsistencies
   - Verify store names are properly formatted

3. **Fix Issues and Re-run**
   - Address any data quality issues
   - Re-run migration script

### Post-Migration Steps

1. **Clean Up Backup Table** (After confirming success)
   ```sql
   -- Remove backup table when confident migration is successful
   DROP TABLE RackMaster_Backup_RackCodeMigration;
   ```

2. **Update Application Code**
   - Ensure frontend/API code uses new RackCode format
   - Update any hardcoded RackCode references

## Migration Logic Details

### Store Prefix Generation
- Takes first letter of each word in store name
- Example: "Raw Material Store" → "RMS"
- Handles special characters and multiple spaces
- Only uses alphabetic characters

### Conflict Resolution
1. **Base Prefix**: Generated from store name
2. **Additional Letters**: If conflict, adds unused letters from store name
3. **Numeric Suffix**: If still conflicts, adds numbers (1, 2, 3...)

### Examples
| Store Name | Base Prefix | Final Prefix | Sample Codes |
|------------|-------------|--------------|--------------|
| Raw Material Store | RMS | RMSR | RMSR-001, RMSR-002 |
| Retail Marketing Store | RMS | RMST | RMST-001, RMST-002 |
| Finished Goods Warehouse | FGW | FGW | FGWR-001, FGWR-002 |

### Records Updated
The migration updates records where RackCode is:
- NULL
- Empty string
- Contains 'temp' or 'placeholder'
- Less than 3 characters long

### Safety Features
- **Transaction-based**: All changes in single transaction
- **Backup Creation**: Automatic backup before changes
- **Validation**: Pre-update validation prevents conflicts
- **Idempotent**: Safe to run multiple times
- **Rollback Support**: Complete rollback capability

## Troubleshooting

### Common Issues

1. **Duplicate Codes Generated**
   - Check validation report for details
   - Verify store names don't have unusual characters
   - Run rollback and investigate data

2. **Invalid Patterns**
   - Some existing codes may not follow new pattern
   - Review validation report for examples
   - Consider manual cleanup of edge cases

3. **Missing Store Names**
   - Ensure all stores have valid StoreName values
   - Check for NULL or empty store names

### Support
For issues or questions about the migration:
1. Review validation report thoroughly
2. Check database logs for error messages
3. Ensure proper database permissions
4. Contact development team if needed

## Testing Recommendations

1. **Test on Development Environment First**
2. **Run Validation Script Before Migration**
3. **Verify Backup Creation**
4. **Test Rollback Process**
5. **Validate Application Functionality Post-Migration**
