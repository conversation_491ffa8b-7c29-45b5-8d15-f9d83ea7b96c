-- =============================================
-- Diagnostic Script for RackMaster Data
-- Purpose: Analyze current RackCode values to understand why migration shows 0 updates
-- Author: PMS System
-- Date: 2025-01-10
-- =============================================

PRINT '========================================';
PRINT 'RackMaster Data Analysis';
PRINT '========================================';
PRINT '';

-- 1. Overall Statistics
PRINT '1. OVERALL STATISTICS';
PRINT '---------------------';

SELECT 
    'Total RackMaster Records' as Metric,
    COUNT(*) as Count
FROM RackMaster
UNION ALL
SELECT 
    'Records with StoreId',
    COUNT(*)
FROM RackMaster 
WHERE StoreId IS NOT NULL
UNION ALL
SELECT 
    'Records with NULL RackCode',
    COUNT(*)
FROM RackMaster 
WHERE RackCode IS NULL
UNION ALL
SELECT 
    'Records with Empty RackCode',
    COUNT(*)
FROM RackMaster 
WHERE RackCode = ''
UNION ALL
SELECT 
    'Records with RackCode < 3 chars',
    COUNT(*)
FROM RackMaster 
WHERE LEN(RackCode) < 3
UNION ALL
SELECT 
    'Records with temp/placeholder',
    COUNT(*)
FROM RackMaster 
WHERE RackCode LIKE '%temp%' OR RackCode LIKE '%placeholder%'
ORDER BY Metric;

PRINT '';

-- 2. Current RackCode Patterns
PRINT '2. CURRENT RACKCODE PATTERNS';
PRINT '-----------------------------';

SELECT 
    'RackCode Pattern Analysis' as Analysis,
    CASE 
        WHEN RackCode IS NULL THEN 'NULL'
        WHEN RackCode = '' THEN 'EMPTY'
        WHEN LEN(RackCode) < 3 THEN 'TOO_SHORT'
        WHEN RackCode LIKE '%temp%' THEN 'TEMP'
        WHEN RackCode LIKE '%placeholder%' THEN 'PLACEHOLDER'
        WHEN RackCode LIKE '%R-[0-9][0-9][0-9]' THEN 'NEW_PATTERN'
        WHEN RackCode LIKE '%-%' THEN 'HAS_DASH'
        WHEN RackCode LIKE '%[0-9]%' THEN 'HAS_NUMBERS'
        ELSE 'OTHER'
    END as Pattern,
    COUNT(*) as Count
FROM RackMaster
GROUP BY 
    CASE 
        WHEN RackCode IS NULL THEN 'NULL'
        WHEN RackCode = '' THEN 'EMPTY'
        WHEN LEN(RackCode) < 3 THEN 'TOO_SHORT'
        WHEN RackCode LIKE '%temp%' THEN 'TEMP'
        WHEN RackCode LIKE '%placeholder%' THEN 'PLACEHOLDER'
        WHEN RackCode LIKE '%R-[0-9][0-9][0-9]' THEN 'NEW_PATTERN'
        WHEN RackCode LIKE '%-%' THEN 'HAS_DASH'
        WHEN RackCode LIKE '%[0-9]%' THEN 'HAS_NUMBERS'
        ELSE 'OTHER'
    END
ORDER BY Count DESC;

PRINT '';

-- 3. Sample RackCode Values
PRINT '3. SAMPLE RACKCODE VALUES';
PRINT '-------------------------';

SELECT TOP 20
    r.RackId,
    s.StoreName,
    r.RackCode,
    LEN(ISNULL(r.RackCode, '')) as CodeLength,
    CASE 
        WHEN r.RackCode IS NULL THEN 'Would Update (NULL)'
        WHEN r.RackCode = '' THEN 'Would Update (EMPTY)'
        WHEN LEN(r.RackCode) < 3 THEN 'Would Update (TOO_SHORT)'
        WHEN r.RackCode LIKE '%temp%' THEN 'Would Update (TEMP)'
        WHEN r.RackCode LIKE '%placeholder%' THEN 'Would Update (PLACEHOLDER)'
        ELSE 'Would NOT Update'
    END as MigrationStatus
FROM RackMaster r
LEFT JOIN StoreMaster s ON r.StoreId = s.StoreId
ORDER BY r.RackId;

PRINT '';

-- 4. Records that WOULD be updated with current criteria
PRINT '4. RECORDS THAT WOULD BE UPDATED (Current Criteria)';
PRINT '---------------------------------------------------';

SELECT 
    COUNT(*) as RecordsToUpdate
FROM RackMaster r
INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
WHERE r.RackCode IS NULL OR
      r.RackCode = '' OR
      r.RackCode LIKE '%temp%' OR
      r.RackCode LIKE '%placeholder%' OR
      LEN(r.RackCode) < 3;

-- Show sample of records that would be updated
SELECT TOP 10
    r.RackId,
    s.StoreName,
    r.RackCode,
    'Current criteria match' as Reason
FROM RackMaster r
INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
WHERE r.RackCode IS NULL OR
      r.RackCode = '' OR
      r.RackCode LIKE '%temp%' OR
      r.RackCode LIKE '%placeholder%' OR
      LEN(r.RackCode) < 3;

PRINT '';

-- 5. Store-wise breakdown
PRINT '5. STORE-WISE BREAKDOWN';
PRINT '-----------------------';

SELECT 
    s.StoreName,
    COUNT(r.RackId) as TotalRacks,
    COUNT(CASE WHEN r.RackCode IS NULL THEN 1 END) as NullCodes,
    COUNT(CASE WHEN r.RackCode = '' THEN 1 END) as EmptyCodes,
    COUNT(CASE WHEN LEN(r.RackCode) < 3 THEN 1 END) as ShortCodes,
    COUNT(CASE WHEN r.RackCode IS NOT NULL AND r.RackCode != '' AND LEN(r.RackCode) >= 3 THEN 1 END) as ValidCodes
FROM StoreMaster s
LEFT JOIN RackMaster r ON s.StoreId = r.StoreId
GROUP BY s.StoreId, s.StoreName
HAVING COUNT(r.RackId) > 0
ORDER BY s.StoreName;

PRINT '';

-- 6. Suggested Migration Criteria
PRINT '6. SUGGESTED MIGRATION CRITERIA OPTIONS';
PRINT '---------------------------------------';

PRINT 'Option 1: Update ALL records (Force regeneration)';
SELECT 
    'ALL Records' as Criteria,
    COUNT(*) as RecordsToUpdate
FROM RackMaster r
INNER JOIN StoreMaster s ON r.StoreId = s.StoreId;

PRINT '';
PRINT 'Option 2: Update records that do NOT follow new pattern';
SELECT 
    'Non-standard Pattern' as Criteria,
    COUNT(*) as RecordsToUpdate
FROM RackMaster r
INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
WHERE r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]';

PRINT '';
PRINT 'Option 3: Update records with specific patterns (customize as needed)';
SELECT 
    'Custom Pattern' as Criteria,
    COUNT(*) as RecordsToUpdate
FROM RackMaster r
INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
WHERE r.RackCode IS NULL OR
      r.RackCode = '' OR
      r.RackCode LIKE '%temp%' OR
      r.RackCode LIKE '%placeholder%' OR
      LEN(r.RackCode) < 3 OR
      r.RackCode NOT LIKE '%-%' OR  -- No dash
      r.RackCode LIKE 'R%' OR       -- Starts with R
      r.RackCode LIKE '%[0-9]' AND r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]'; -- Has numbers but wrong pattern

PRINT '';
PRINT '========================================';
PRINT 'Analysis Complete';
PRINT '';
PRINT 'RECOMMENDATIONS:';
PRINT '1. Review the sample RackCode values above';
PRINT '2. Choose appropriate migration criteria based on your data';
PRINT '3. Modify the migration script criteria if needed';
PRINT '4. Test with a small subset first';
PRINT '========================================';
