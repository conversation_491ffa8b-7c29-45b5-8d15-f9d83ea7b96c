-- =============================================
-- RackMaster RackCode Migration Validation Script
-- Purpose: Validate the results of the RackCode migration
-- Author: PMS System
-- Date: 2025-01-10
-- =============================================

PRINT '========================================';
PRINT 'RackCode Migration Validation Report';
PRINT '========================================';
PRINT '';

-- 1. Overall Statistics
PRINT '1. OVERALL STATISTICS';
PRINT '---------------------';

SELECT 
    'Total Records' as Metric,
    COUNT(*) as Count
FROM RackMaster
UNION ALL
SELECT 
    'Records with RackCode',
    COUNT(*)
FROM RackMaster 
WHERE RackCode IS NOT NULL AND RackCode != ''
UNION ALL
SELECT 
    'Records without RackCode',
    COUNT(*)
FROM RackMaster 
WHERE RackCode IS NULL OR RackCode = ''
UNION ALL
SELECT 
    'Records following new pattern',
    COUNT(*)
FROM RackMaster 
WHERE RackCode LIKE '%R-[0-9][0-9][0-9]'
ORDER BY Metric;

PRINT '';

-- 2. Pattern Validation
PRINT '2. PATTERN VALIDATION';
PRINT '---------------------';

-- Check for records that don't follow the expected pattern
SELECT 
    'Invalid Pattern' as Issue,
    COUNT(*) as Count,
    'RackCodes that do not follow {Prefix}R-{3digits} pattern' as Description
FROM RackMaster 
WHERE RackCode IS NOT NULL 
  AND RackCode != ''
  AND RackCode NOT LIKE '%R-[0-9][0-9][0-9]'
UNION ALL
SELECT 
    'Valid Pattern',
    COUNT(*),
    'RackCodes that follow the correct pattern'
FROM RackMaster 
WHERE RackCode LIKE '%R-[0-9][0-9][0-9]';

-- Show examples of invalid patterns if any exist
IF EXISTS (SELECT 1 FROM RackMaster WHERE RackCode IS NOT NULL AND RackCode != '' AND RackCode NOT LIKE '%R-[0-9][0-9][0-9]')
BEGIN
    PRINT '';
    PRINT 'Examples of Invalid Patterns:';
    SELECT TOP 10
        r.RackId,
        s.StoreName,
        r.RackCode,
        'Expected pattern: {StorePrefix}R-{3digits}' as ExpectedFormat
    FROM RackMaster r
    INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NOT NULL 
      AND r.RackCode != ''
      AND r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]'
    ORDER BY r.StoreId, r.RackId;
END

PRINT '';

-- 3. Duplicate Detection
PRINT '3. DUPLICATE DETECTION';
PRINT '----------------------';

-- Check for duplicate RackCodes within the same store
SELECT 
    'Duplicates within Store' as Issue,
    COUNT(*) as StoresAffected,
    SUM(DuplicateCount - 1) as TotalDuplicates
FROM (
    SELECT 
        StoreId,
        RackCode,
        COUNT(*) as DuplicateCount
    FROM RackMaster 
    WHERE RackCode IS NOT NULL AND RackCode != ''
    GROUP BY StoreId, RackCode
    HAVING COUNT(*) > 1
) duplicates;

-- Show duplicate details if any exist
IF EXISTS (
    SELECT 1 
    FROM RackMaster 
    WHERE RackCode IS NOT NULL AND RackCode != ''
    GROUP BY StoreId, RackCode
    HAVING COUNT(*) > 1
)
BEGIN
    PRINT '';
    PRINT 'Duplicate RackCode Details:';
    SELECT 
        s.StoreName,
        r.RackCode,
        COUNT(*) as DuplicateCount,
        STRING_AGG(CAST(r.RackId AS VARCHAR(10)), ', ') as RackIds
    FROM RackMaster r
    INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NOT NULL AND r.RackCode != ''
    GROUP BY s.StoreName, r.RackCode, r.StoreId
    HAVING COUNT(*) > 1
    ORDER BY s.StoreName, r.RackCode;
END

PRINT '';

-- 4. Store-wise Summary
PRINT '4. STORE-WISE SUMMARY';
PRINT '---------------------';

SELECT 
    s.StoreName,
    COUNT(r.RackId) as TotalRacks,
    COUNT(CASE WHEN r.RackCode IS NOT NULL AND r.RackCode != '' THEN 1 END) as RacksWithCode,
    COUNT(CASE WHEN r.RackCode LIKE '%R-[0-9][0-9][0-9]' THEN 1 END) as RacksWithValidPattern,
    MIN(r.RackCode) as FirstRackCode,
    MAX(r.RackCode) as LastRackCode
FROM StoreMaster s
LEFT JOIN RackMaster r ON s.StoreId = r.StoreId
GROUP BY s.StoreId, s.StoreName
HAVING COUNT(r.RackId) > 0
ORDER BY s.StoreName;

PRINT '';

-- 5. Prefix Analysis
PRINT '5. PREFIX ANALYSIS';
PRINT '------------------';

-- Extract and analyze prefixes
WITH PrefixAnalysis AS (
    SELECT 
        s.StoreName,
        r.RackCode,
        CASE 
            WHEN r.RackCode LIKE '%R-[0-9][0-9][0-9]'
            THEN LEFT(r.RackCode, LEN(r.RackCode) - 6)  -- Remove 'R-XXX' part
            ELSE 'INVALID'
        END as ExtractedPrefix
    FROM RackMaster r
    INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NOT NULL AND r.RackCode != ''
)
SELECT 
    StoreName,
    ExtractedPrefix,
    COUNT(*) as RackCount,
    MIN(RackCode) as SampleRackCode
FROM PrefixAnalysis
GROUP BY StoreName, ExtractedPrefix
ORDER BY StoreName, ExtractedPrefix;

PRINT '';

-- 6. Sequence Number Analysis
PRINT '6. SEQUENCE NUMBER ANALYSIS';
PRINT '---------------------------';

-- Check for gaps in sequence numbers within each store
WITH SequenceAnalysis AS (
    SELECT 
        s.StoreName,
        r.StoreId,
        CASE 
            WHEN r.RackCode LIKE '%R-[0-9][0-9][0-9]'
            THEN CAST(RIGHT(r.RackCode, 3) AS INT)
            ELSE NULL
        END as SequenceNumber,
        LEFT(r.RackCode, LEN(r.RackCode) - 6) as Prefix
    FROM RackMaster r
    INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode LIKE '%R-[0-9][0-9][0-9]'
)
SELECT 
    StoreName,
    Prefix,
    COUNT(*) as TotalRacks,
    MIN(SequenceNumber) as MinSequence,
    MAX(SequenceNumber) as MaxSequence,
    CASE 
        WHEN MAX(SequenceNumber) - MIN(SequenceNumber) + 1 = COUNT(*)
        THEN 'No Gaps'
        ELSE 'Has Gaps'
    END as SequenceStatus
FROM SequenceAnalysis
WHERE SequenceNumber IS NOT NULL
GROUP BY StoreName, StoreId, Prefix
ORDER BY StoreName, Prefix;

PRINT '';

-- 7. Migration Comparison (if backup exists)
PRINT '7. MIGRATION COMPARISON';
PRINT '-----------------------';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RackMaster_Backup_RackCodeMigration]') AND type in (N'U'))
BEGIN
    SELECT 
        'Records in Current Table' as Metric,
        COUNT(*) as Count
    FROM RackMaster
    UNION ALL
    SELECT 
        'Records in Backup Table',
        COUNT(*)
    FROM RackMaster_Backup_RackCodeMigration
    UNION ALL
    SELECT 
        'Records Modified',
        COUNT(*)
    FROM RackMaster r
    INNER JOIN RackMaster_Backup_RackCodeMigration b ON r.RackId = b.RackId
    WHERE r.RackCode != b.RackCode 
       OR (r.RackCode IS NULL AND b.RackCode IS NOT NULL)
       OR (r.RackCode IS NOT NULL AND b.RackCode IS NULL);
    
    PRINT '';
    PRINT 'Sample of Modified Records:';
    SELECT TOP 10
        r.RackId,
        s.StoreName,
        b.RackCode as OldRackCode,
        r.RackCode as NewRackCode
    FROM RackMaster r
    INNER JOIN RackMaster_Backup_RackCodeMigration b ON r.RackId = b.RackId
    INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode != b.RackCode 
       OR (r.RackCode IS NULL AND b.RackCode IS NOT NULL)
       OR (r.RackCode IS NOT NULL AND b.RackCode IS NULL)
    ORDER BY r.StoreId, r.RackId;
END
ELSE
BEGIN
    PRINT 'Backup table not found. Cannot compare migration results.';
END

PRINT '';
PRINT '========================================';
PRINT 'Validation Report Complete';
PRINT '========================================';
