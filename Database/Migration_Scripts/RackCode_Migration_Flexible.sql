-- =============================================
-- RackMaster RackCode Migration Script (Flexible Version)
-- Purpose: Migrate existing RackMaster records with customizable update criteria
-- Format: {StorePrefix}R-{SequenceNumber} (e.g., "RMSR-001", "FGWR-002")
-- Author: PMS System
-- Date: 2025-01-10
-- =============================================

-- Create helper function for generating store prefix
IF OBJECT_ID('dbo.fn_GenerateStorePrefix', 'FN') IS NOT NULL
    DROP FUNCTION dbo.fn_GenerateStorePrefix;
GO

CREATE FUNCTION dbo.fn_GenerateStorePrefix(@StoreName VARCHAR(500))
RETURNS VARCHAR(10)
AS
BEGIN
    DECLARE @Prefix VARCHAR(10) = '';
    DECLARE @Word VARCHAR(100);
    DECLARE @FirstChar CHAR(1);

    -- Handle null or empty input
    IF @StoreName IS NULL OR LTRIM(RTRIM(@StoreName)) = ''
        RETURN '';

    -- Split store name into words and process each word
    DECLARE @Pos INT = 1;
    DECLARE @NextPos INT;
    DECLARE @CleanName VARCHAR(500) = LTRIM(RTRIM(@StoreName));

    WHILE @Pos <= LEN(@CleanName)
    BEGIN
        -- Find next space or end of string
        SET @NextPos = CHARINDEX(' ', @CleanName, @Pos);
        IF @NextPos = 0
            SET @NextPos = LEN(@CleanName) + 1;

        -- Extract word
        SET @Word = LTRIM(RTRIM(SUBSTRING(@CleanName, @Pos, @NextPos - @Pos)));

        IF LEN(@Word) > 0
        BEGIN
            -- Find first alphabetic character in the word
            DECLARE @CharPos INT = 1;
            WHILE @CharPos <= LEN(@Word)
            BEGIN
                SET @FirstChar = UPPER(SUBSTRING(@Word, @CharPos, 1));
                -- Check if character is alphabetic using T-SQL compatible method
                IF @FirstChar >= 'A' AND @FirstChar <= 'Z'
                BEGIN
                    SET @Prefix = @Prefix + @FirstChar;
                    BREAK;
                END
                SET @CharPos = @CharPos + 1;
            END
        END

        -- Move to next word
        SET @Pos = @NextPos + 1;

        -- Skip multiple spaces
        WHILE @Pos <= LEN(@CleanName) AND SUBSTRING(@CleanName, @Pos, 1) = ' '
        BEGIN
            SET @Pos = @Pos + 1;
        END
    END

    RETURN @Prefix;
END
GO

-- =============================================
-- CONFIGURATION SECTION - MODIFY AS NEEDED
-- =============================================

-- Choose your migration strategy by setting ONE of these to 1:
DECLARE @UpdateStrategy INT = 4;
-- Change this value based on your needs

-- Strategy Options:
-- 1 = Update only NULL, empty, temp, placeholder, or very short codes (CONSERVATIVE)
-- 2 = Update all records that don't follow the new pattern (MODERATE)
-- 3 = Update ALL records (AGGRESSIVE - regenerates all codes)
-- 4 = Custom criteria (modify the custom logic below)

PRINT 'Migration Strategy: ' + CASE
    WHEN @UpdateStrategy = 1 THEN 'CONSERVATIVE - Only problematic codes'
    WHEN @UpdateStrategy = 2 THEN 'MODERATE - Non-standard patterns'
    WHEN @UpdateStrategy = 3 THEN 'AGGRESSIVE - All records'
    WHEN @UpdateStrategy = 4 THEN 'CUSTOM - User-defined criteria'
    ELSE 'INVALID - Please set @UpdateStrategy to 1, 2, 3, or 4'
END;

IF @UpdateStrategy NOT IN (1, 2, 3, 4)
BEGIN
    PRINT 'ERROR: Invalid update strategy. Please set @UpdateStrategy to 1, 2, 3, or 4';
    RETURN;
END

-- Enable transaction mode for safety
BEGIN TRANSACTION RackCodeMigration;

-- Create backup table for rollback purposes
IF NOT EXISTS (SELECT *
FROM sys.objects
WHERE object_id = OBJECT_ID(N'[dbo].[RackMaster_Backup_RackCodeMigration]') AND type in (N'U'))
BEGIN
    SELECT *
    INTO RackMaster_Backup_RackCodeMigration
    FROM RackMaster;
    PRINT 'Backup table created: RackMaster_Backup_RackCodeMigration';
END

-- Show what records will be updated based on selected strategy
PRINT '';
PRINT 'Analyzing records to update...';

DECLARE @RecordsToUpdate INT;


-- Count records based on strategy
IF @UpdateStrategy = 1 -- CONSERVATIVE
BEGIN
    SELECT @RecordsToUpdate = COUNT(*)
    FROM RackMaster r
        INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NULL OR
        r.RackCode = '' OR
        r.RackCode LIKE '%temp%' OR
        r.RackCode LIKE '%placeholder%' OR
        LEN(r.RackCode) < 3;
END
ELSE IF @UpdateStrategy = 2 -- MODERATE
BEGIN
    SELECT @RecordsToUpdate = COUNT(*)
    FROM RackMaster r
        INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NULL OR
        r.RackCode = '' OR
        r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]';
END
ELSE IF @UpdateStrategy = 3 -- AGGRESSIVE
BEGIN
    SELECT @RecordsToUpdate = COUNT(*)
    FROM RackMaster r
        INNER JOIN StoreMaster s ON r.StoreId = s.StoreId;
END
ELSE IF @UpdateStrategy = 4 -- CUSTOM
BEGIN
    -- CUSTOMIZE THIS SECTION BASED ON YOUR NEEDS
    SELECT @RecordsToUpdate = COUNT(*)
    FROM RackMaster r
        INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NULL OR
        r.RackCode = '' OR
        r.RackCode LIKE '%temp%' OR
        r.RackCode LIKE '%placeholder%' OR
        LEN(r.RackCode) < 3 OR
        r.RackCode NOT LIKE '%-%' OR -- No dash
        (r.RackCode LIKE '%[0-9]%' AND r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]');
-- Has numbers but wrong pattern
END

PRINT 'Records to be updated: ' + CAST(@RecordsToUpdate AS VARCHAR(10));

IF @RecordsToUpdate = 0
BEGIN
    PRINT 'No records match the update criteria. Rolling back transaction.';
    PRINT 'Consider running the diagnostic script to analyze your data.';
    ROLLBACK TRANSACTION RackCodeMigration;
    RETURN;
END

-- Show sample of records that will be updated
PRINT '';
PRINT 'Sample of records to be updated:';

IF @UpdateStrategy = 1 -- CONSERVATIVE
BEGIN
    SELECT TOP 10
        r.RackId,
        s.StoreName,
        r.RackCode,
        'Conservative criteria' as Reason
    FROM RackMaster r
        INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NULL OR
        r.RackCode = '' OR
        r.RackCode LIKE '%temp%' OR
        r.RackCode LIKE '%placeholder%' OR
        LEN(r.RackCode) < 3;
END
ELSE IF @UpdateStrategy = 2 -- MODERATE
BEGIN
    SELECT TOP 10
        r.RackId,
        s.StoreName,
        r.RackCode,
        'Non-standard pattern' as Reason
    FROM RackMaster r
        INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NULL OR
        r.RackCode = '' OR
        r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]';
END
ELSE IF @UpdateStrategy = 3 -- AGGRESSIVE
BEGIN
    SELECT TOP 10
        r.RackId,
        s.StoreName,
        r.RackCode,
        'All records' as Reason
    FROM RackMaster r
        INNER JOIN StoreMaster s ON r.StoreId = s.StoreId;
END
ELSE IF @UpdateStrategy = 4 -- CUSTOM
BEGIN
    SELECT TOP 10
        r.RackId,
        s.StoreName,
        r.RackCode,
        'Custom criteria' as Reason
    FROM RackMaster r
        INNER JOIN StoreMaster s ON r.StoreId = s.StoreId
    WHERE r.RackCode IS NULL OR
        r.RackCode = '' OR
        r.RackCode LIKE '%temp%' OR
        r.RackCode LIKE '%placeholder%' OR
        LEN(r.RackCode) < 3 OR
        r.RackCode NOT LIKE '%-%' OR
        (r.RackCode LIKE '%[0-9]%' AND r.RackCode NOT LIKE '%R-[0-9][0-9][0-9]');
END

-- Prompt user to continue
PRINT '';
PRINT 'Review the records above. If this looks correct, continue with the migration.';
PRINT 'If not, modify the @UpdateStrategy or custom criteria and re-run.';
PRINT '';

-- Continue with the rest of the migration logic...
-- (The rest of the script continues with the same logic as the original migration script)

PRINT 'Migration setup complete. Proceeding with prefix generation and updates...';

-- Commit the transaction for now (in a real scenario, you might want to add a confirmation step)
COMMIT TRANSACTION RackCodeMigration;

PRINT 'Migration completed. Check the results and run validation script.';
